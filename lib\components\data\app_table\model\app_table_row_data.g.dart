// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'app_table_row_data.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

AppTableRowData _$AppTableRowDataFromJson(Map<String, dynamic> json) =>
    AppTableRowData(
      data: json['data'] as Map<String, dynamic>,
      level: (json['level'] as num).toInt(),
      hasChildren: json['hasChildren'] as bool,
      isExpanded: json['isExpanded'] as bool? ?? false,
      parentId: json['parentId'] as String?,
      id: json['id'] as String,
      children:
          (json['children'] as List<dynamic>?)
              ?.map((e) => AppTableRowData.fromJson(e as Map<String, dynamic>))
              .toList(),
      levelOneIndex: (json['levelOneIndex'] as num?)?.toInt(),
      childrenCount: (json['childrenCount'] as num?)?.toInt() ?? 0,
      descendantsCount: (json['descendantsCount'] as num?)?.toInt() ?? 0,
      isLeaf: json['isLeaf'] as bool? ?? true,
      isChecked: json['isChecked'] as bool? ?? false,
    );

Map<String, dynamic> _$AppTableRowDataToJson(AppTableRowData instance) =>
    <String, dynamic>{
      'data': instance.data,
      'level': instance.level,
      'hasChildren': instance.hasChildren,
      'isExpanded': instance.isExpanded,
      'parentId': instance.parentId,
      'id': instance.id,
      'children': instance.children,
      'levelOneIndex': instance.levelOneIndex,
      'childrenCount': instance.childrenCount,
      'descendantsCount': instance.descendantsCount,
      'isLeaf': instance.isLeaf,
      'isChecked': instance.isChecked,
    };
