import 'package:json_annotation/json_annotation.dart';

part 'employee.g.dart';

@JsonSerializable()
class Employee {
  @Json<PERSON>ey(name: 'EmployeeId')
  String? employeeId;

  @<PERSON><PERSON><PERSON><PERSON>(name: 'Name', defaultValue: '')
  String name;

  @<PERSON><PERSON><PERSON><PERSON>(name: 'Avatar', defaultValue: '')
  String avatar;

  @Json<PERSON><PERSON>(name: 'Number', defaultValue: '')
  String number;

  Employee({
    this.employeeId,
    this.name = '',
    this.avatar = '',
    this.number = '',
  });

  factory Employee.fromJson(Map<String, dynamic> json) =>
      _$EmployeeFromJson(json);
  @override
  Map<String, dynamic> toJson() => _$EmployeeToJson(this);
}
