import 'package:flutter/cupertino.dart';
import 'package:octasync_client/components/data/app_table/app_date.dart';
import 'package:octasync_client/components/data/app_table/app_select.dart';
import 'package:octasync_client/components/data/app_table/enums/cell_state_enum.dart';
import 'package:octasync_client/components/data/app_table/enums/select_enum.dart';
import 'package:octasync_client/components/data/app_table/helper/app_table_common.dart';
import 'package:octasync_client/components/data/app_table/helper/app_table_general_helper.dart';
import 'package:octasync_client/components/data/app_table/model/app_table_column.dart';
import 'package:octasync_client/components/data/app_table/model/app_table_column_type_with_number_format.dart';
import 'package:octasync_client/components/data/app_table/model/column_types/app_table_column_type_date.dart';
import 'package:octasync_client/components/data/app_table/model/column_types/app_table_column_type_number.dart';
import 'package:octasync_client/components/data/app_table/model/column_types/app_table_column_type_percentage.dart';
import 'package:octasync_client/components/data/app_table/model/column_types/app_table_column_type_single_select.dart';
import 'package:octasync_client/components/data/app_table/model/general_option.dart';
import 'package:intl/intl.dart';

class SelectCell extends StatefulWidget {
  const SelectCell({
    super.key,
    this.state = CellStateEnum.normal,
    required this.column,
    required this.rowData,
    required this.rowIdx,
    required this.columnIdx,
    required this.rowId,
    required this.columnId,
  });

  final CellStateEnum state;

  final AppTableColumn column;

  final Map<String, dynamic> rowData;

  final int rowIdx;

  final int columnIdx;

  final String rowId;
  final String columnId;

  @override
  State<SelectCell> createState() => _SelectCellState();
}

class _SelectCellState extends State<SelectCell> {
  late AppTableColumn column;
  late AppTableColumnTypeSingleSelect columnObj;
  late Map<String, dynamic> rowData;
  late String rowId;
  late String columnId;

  @override
  void initState() {
    // TODO: implement initState
    super.initState();
    column = widget.column;
    columnObj = column.type as AppTableColumnTypeSingleSelect;
    rowData = widget.rowData;
    rowId = widget.rowId;
    columnId = widget.columnId;
  }

  @override
  Widget build(BuildContext context) {
    // return Text('测试');
    //是否多选
    var isMultipleSelect = columnObj.subType == SelectEnum.multiple;

    dynamic cellValue = rowData[column.field];
    var value = cellValue;

    //备选项
    var options = columnObj.options;

    // 默认值类型（1：无；2：指定值）
    var defaultValueType = columnObj.defaultValueType;

    // print(
    //   'options==${options} isMultipleSelect==${isMultipleSelect} value==${value}',
    // );

    // print(
    //   'value=${value} options=${options.map((c) => c.text).join(',')} isMultipleSelect=${isMultipleSelect} defaultValueType=${defaultValueType}',
    // );

    // DateTime? dateTime;

    // if (value is DateTime) {
    //   dateTime = value;
    // } else if (value is String) {
    //   dateTime = DateTime.tryParse(value);
    // } else if (value == null) {
    //   dateTime = null;
    // }

    // String _format =
    //     AppTableCommon.getDateFormatOption(columnObj.dateType).text +
    //     (columnObj.showTime ? ' HH:mm' : '');

    var state = widget.state;
    if (state == CellStateEnum.edit) {
      if (isMultipleSelect) {
        List<String> selectedValueList =
            value != null ? (value as List).map((c) => c as String).toList() : [];
        return AppSelect<String>(
          key: ValueKey('cell_${rowId}${columnId}'),
          options: options,
          value: selectedValueList,
          // isMultiple: isMultipleSelect,
          subType: SelectEnum.multiple,
          onChanged: (item) {
            rowData[column.field] = item;
          },
        );
      } else {
        // 单选模式，确保 value 是正确的字符串类型
        // String selectedValue = value ?? '';
        String? selectedValue = null;
        if (value != null) {
          if (value is String) {
            selectedValue = value;
          } else if (value is List && value.isNotEmpty) {
            selectedValue = value.first.toString();
          } else {
            selectedValue = value.toString();
          }
        }
        return AppSelect<String>(
          key: ValueKey('cell_${rowId}${columnId}'),
          options: options,
          value: selectedValue,
          // isMultiple: isMultipleSelect,
          subType: SelectEnum.single,
          onChanged: (item) {
            rowData[column.field] = item;
          },
        );
      }
    } else {
      // var str = columnObj.formatStr;

      if (value != null) {
        var result = '';
        if (isMultipleSelect) {
          List<String> selectList = (value as List).map((c) => c as String).toList();

          result =
              options
                  .where((c) => selectList.contains(c.id))
                  .map((c) => c.text)
                  .join(',')
                  .toString();
        } else {
          result =
              options
                  .firstWhere((c) => c.id == value, orElse: () => GeneralOption(id: '-1', text: ''))
                  .text;
        }
        return Text(result);
      } else {
        // 处理空值或无效值的情况
        return Text('');
      }
    }
  }
}
