import 'package:flutter/cupertino.dart';
import 'package:octasync_client/components/data/app_table/app_table_column_type.dart';
import 'package:octasync_client/components/data/app_table/enums/column_type_enum.dart';
import 'package:octasync_client/components/data/app_table/model/app_table_column.dart';
import 'package:intl/intl.dart';
import 'package:uuid/uuid.dart';

class AppTableGeneralHelper {
  static int compareWithNull(dynamic a, dynamic b, int Function() resolve) {
    if (a == null || b == null) {
      return a == b
          ? 0
          : a == null
          ? -1
          : 1;
    }
    return resolve();
  }

  /// 添加新列
  /// /// [tableViewportWidth] 当前表格的可视区域宽度
  static AppTableColumn newColumn({ColumnTypeEnum type = ColumnTypeEnum.text, String text = ''}) {
    final Uuid _uuid = Uuid();
    final String newField = _uuid.v4();
    double columnWidth = 200;
    final newColumn = AppTableColumn(
      field: newField,
      text: text,
      typeCode: type,
      type: newColumnType(type: type),
      width: columnWidth,
      resizable: true,
    );

    // switch (type) {
    //   case ColumnTypeEnum.text:
    //     newColumn.type = AppTableColumnType.text();
    //     break;
    //   case ColumnTypeEnum.number:
    //     newColumn.type = AppTableColumnType.number();
    //     break;
    //   default:
    //     newColumn.type = AppTableColumnType.text();
    //     break;
    // }

    return newColumn;

    // _columns.add(newColumn);
    // // _columnsKeys.add(GlobalKey());

    // // 给所有行数据补充新字段
    // for (var row in _allRows) {
    //   row.data[newField] = '';
    // }

    // /// 避免添加列后，把添加列按钮顶出可视区域
    // if (tableViewportWidth <
    //     tableContentWidth + horizontalScrollController.offset) {
    //   horizontalScrollController.jumpTo(
    //     horizontalScrollController.offset + columnWidth,
    //   );
    // }

    // _markColumnsChanged();
    // notifyListeners();
  }

  static AppTableColumnType newColumnType({ColumnTypeEnum type = ColumnTypeEnum.text}) {
    switch (type) {
      case ColumnTypeEnum.text:
        return AppTableColumnType.text();
      case ColumnTypeEnum.number:
        return AppTableColumnType.number();
      case ColumnTypeEnum.date:
        return AppTableColumnType.date();
      case ColumnTypeEnum.percentage:
        return AppTableColumnType.percentage();
      case ColumnTypeEnum.checkbox:
        return AppTableColumnType.checkbox();
      case ColumnTypeEnum.singleSelect:
        return AppTableColumnType.singleSelect();
      case ColumnTypeEnum.multipleSelect:
        return AppTableColumnType.multipleSelect();
      case ColumnTypeEnum.hyperlink:
        return AppTableColumnType.hyperlink();
      case ColumnTypeEnum.parentRecord:
        return AppTableColumnType.parentRecord();
      case ColumnTypeEnum.telephone:
        return AppTableColumnType.telephone();
      case ColumnTypeEnum.currency:
        return AppTableColumnType.currency();

      // case ColumnTypeEnum.time:
      //   return AppTableColumnType.time();
      // case ColumnTypeEnum.datetime:
      //   return AppTableColumnType.datetime();
      default:
        return AppTableColumnType.text();
    }
  }

  /// 展示错误信息（替换成 tooltip 组件）
  static void showMessage(String msg) {
    print(msg);
  }

  /// 格式化数字
  /// [num] 需要格式化的数字
  /// [isShowPercentiles] 是否显示千分位
  /// [precision] 保留小数位数
  /// [isRetainDecimal] 是否强制保留小数数位（不足补0）
  /// [suffix] 格式化数据后面追加符号
  static String getNumberFormatStr(
    num num, {
    bool isShowPercentiles = true,
    int precision = 0,
    bool isRetainDecimal = false,
    String suffix = '',
  }) {
    var format = getFormatStr(
      isShowPercentiles: isShowPercentiles,
      precision: precision,
      isRetainDecimal: isRetainDecimal,
    );
    return '${NumberFormat(format).format(num)}${suffix}';
  }

  /// 返回需要格式化数字的格式化字符
  /// [isShowPercentiles] 是否显示千分位
  /// [precision] 保留小数位数
  /// [isRetainDecimal] 是否强制保留小数数位（不足补0）
  static String getFormatStr({
    bool isShowPercentiles = true,
    int precision = 0,
    bool isRetainDecimal = false,
  }) {
    String str = '0'; // 不显示千分位
    if (isShowPercentiles) {
      str = '#,##0'; //显示千分位
    }

    //保留小数位数
    if (precision > 0) {
      str += '.';

      for (var i = 0; i < precision; i++) {
        str += (isRetainDecimal ? '0' : '#');
      }
    }

    return str;
  }
}
