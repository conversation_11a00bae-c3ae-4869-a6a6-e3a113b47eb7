import 'package:flutter/material.dart';
import 'package:octasync_client/api/department.dart';
import 'package:octasync_client/models/department/department_model.dart';
import 'package:octasync_client/models/pages_model/pages_model.dart';
import 'department_tree_style.dart';

// 常量定义
class _DepartmentTreeConstants {
  static const int defaultPageSize = 9999;
  static const String loadingMessage = '正在加载部门数据...';
  static const String emptyDataMessage = '暂无部门数据';
  static const String noSearchResultMessage = '暂无数据';
  static const String retryButtonText = '重试';
  static const String loadErrorPrefix = '加载部门数据失败: ';
}

/// 部门树节点数据模型
class DepartmentTreeNode {
  final DepartmentModel department;
  final List<DepartmentTreeNode> children;
  bool isExpanded;
  bool isSelected; // 节点点击选择状态（高亮显示）
  bool isChecked; // 复选框选中状态
  bool isIndeterminate; // 复选框半选状态
  bool isVisible; // 搜索时节点是否可见

  // 缓存计算结果以提高性能
  int? _cachedLevel;
  int? _cachedCheckedCount;

  DepartmentTreeNode({
    required this.department,
    List<DepartmentTreeNode>? children,
    this.isExpanded = false,
    this.isSelected = false,
    this.isChecked = false,
    this.isIndeterminate = false,
    this.isVisible = true,
  }) : children = children ?? [];

  /// 检查是否为叶子节点
  bool get isLeaf => children.isEmpty;

  /// 获取节点层级深度（带缓存）
  int get level {
    _cachedLevel ??= department.parentIdList.length;
    return _cachedLevel!;
  }

  /// 获取所有被选中的子节点数量（带缓存）
  int get checkedChildrenCount {
    // 当子节点状态发生变化时，需要清除缓存
    _cachedCheckedCount ??= children.where((child) => child.isChecked).length;
    return _cachedCheckedCount!;
  }

  /// 清除缓存（当子节点状态变化时调用）
  void clearCache() {
    _cachedCheckedCount = null;
  }

  /// 获取所有子节点数量
  int get totalChildrenCount => children.length;

  /// 检查是否有部分子节点被选中
  bool get hasPartiallyCheckedChildren {
    if (children.isEmpty) return false;
    final checkedCount = checkedChildrenCount;
    return checkedCount > 0 && checkedCount < totalChildrenCount;
  }

  /// 检查是否所有子节点都被选中
  bool get hasAllChildrenChecked {
    if (children.isEmpty) return false;
    return checkedChildrenCount == totalChildrenCount;
  }

  /// 复制节点状态（用于状态管理）
  void copyStateFrom(DepartmentTreeNode other) {
    isExpanded = other.isExpanded;
    isSelected = other.isSelected;
    isChecked = other.isChecked;
    isIndeterminate = other.isIndeterminate;
    isVisible = other.isVisible;
    clearCache();
  }
}

///树状选择器组件
class DepartmentTree extends StatefulWidget {
  /// 是否显示复选框
  final bool showCheckbox;

  /// 节点点击回调
  final void Function(DepartmentModel department)? onNodeTap;

  /// 节点选择回调（复选框点击时触发）
  final void Function(DepartmentModel department, bool isSelected)? onNodeSelected;

  /// 搜索查询字符串
  final String? searchQuery;

  /// 数据加载完成回调
  final VoidCallback? onDataLoaded;

  const DepartmentTree({
    super.key,
    this.showCheckbox = false,
    this.onNodeTap,
    this.onNodeSelected,
    this.searchQuery,
    this.onDataLoaded,
  });

  @override
  State<DepartmentTree> createState() => DepartmentTreeState();
}

// 将 State 类改为公开，以便外部可以访问
class DepartmentTreeState extends State<DepartmentTree> {
  final Map<String, dynamic> _reqParams = {
    'PageIndex': 1,
    'PageSize': _DepartmentTreeConstants.defaultPageSize,
  };

  PagesModel<DepartmentModel> _pages = PagesModel();
  List<DepartmentModel> _list = [];
  List<DepartmentTreeNode> _treeNodes = [];
  String? _currentSearchQuery;

  // 性能优化：缓存节点映射
  Map<String, DepartmentTreeNode> _nodeMap = {};

  // 加载状态管理
  bool _isLoading = false;
  String? _errorMessage;

  @override
  void initState() {
    super.initState();
    _currentSearchQuery = widget.searchQuery;
    _loadDepartmentData();
  }

  @override
  void didUpdateWidget(DepartmentTree oldWidget) {
    super.didUpdateWidget(oldWidget);
    // 当搜索查询发生变化时，更新搜索结果
    if (widget.searchQuery != _currentSearchQuery) {
      _currentSearchQuery = widget.searchQuery;
      _applySearchFilter();
    }
  }

  @override
  void dispose() {
    // 清理资源
    _nodeMap.clear();
    super.dispose();
  }

  /// 刷新部门列表数据 - 公开方法供外部调用
  void refresh() {
    _loadDepartmentData();
  }

  /// 加载部门数据
  Future<void> _loadDepartmentData() async {
    if (_isLoading) return; // 防止重复加载

    setState(() {
      _isLoading = true;
      _errorMessage = null;
    });

    try {
      final response = await DepartmentApi.getList(_reqParams);
      if (mounted) {
        setState(() {
          _pages = PagesModel.fromJson(
            response,
            (json) => DepartmentModel.fromJson(json as Map<String, dynamic>),
          );
          _list = _pages.items;
          _buildTreeStructure();
          _applySearchFilter(); // 构建树结构后应用搜索过滤
          _isLoading = false;
        });
        // 数据加载完成后调用回调
        widget.onDataLoaded?.call();
      }
    } catch (error) {
      if (mounted) {
        setState(() {
          _errorMessage = '${_DepartmentTreeConstants.loadErrorPrefix}$error';
          _isLoading = false;
        });
      }
    }
  }

  /// 获取部门列表数据（保持向后兼容）
  @Deprecated('Use _loadDepartmentData instead')
  void getList() {
    _loadDepartmentData();
  }

  /// 构建树形结构
  void _buildTreeStructure() {
    // 清空之前的缓存
    _nodeMap.clear();

    // 预分配容量以提高性能
    final nodeMap = <String, DepartmentTreeNode>{};
    final rootNodes = <DepartmentTreeNode>[];

    // 第一遍：创建所有节点
    for (final dept in _list) {
      if (dept.id?.isNotEmpty == true) {
        final node = DepartmentTreeNode(
          department: dept,
          children: <DepartmentTreeNode>[], // 创建可变列表
        );
        nodeMap[dept.id!] = node;
      }
    }

    // 第二遍：构建父子关系
    for (final dept in _list) {
      if (dept.id?.isNotEmpty != true) continue;

      final currentNode = nodeMap[dept.id!];
      if (currentNode == null) continue;

      if (dept.parentId?.isNotEmpty == true) {
        // 子节点：添加到父节点
        final parentNode = nodeMap[dept.parentId!];
        parentNode?.children.add(currentNode);
      } else {
        // 根节点
        rootNodes.add(currentNode);
      }
    }

    // 缓存节点映射以供后续查找使用
    _nodeMap = nodeMap;
    _treeNodes = rootNodes;

    // 默认展开并选中第一个根节点
    if (rootNodes.isNotEmpty) {
      final firstRoot = rootNodes.first;
      firstRoot.isExpanded = true;
      firstRoot.isSelected = true;
      _handleNodeTap(firstRoot);
    }
  }

  /// 应用搜索过滤
  void _applySearchFilter() {
    final query = _currentSearchQuery?.trim();

    if (query?.isEmpty != false) {
      // 没有搜索条件时，显示所有节点
      _resetAllNodesVisibility();
    } else {
      // 有搜索条件时，应用过滤
      final lowerQuery = query!.toLowerCase();
      _filterNodes(_treeNodes, lowerQuery);
    }

    // 只在需要时调用setState
    if (mounted) {
      setState(() {});
    }
  }

  /// 重置所有节点的可见性
  void _resetAllNodesVisibility() {
    // 使用迭代而非递归，避免栈溢出
    final stack = <DepartmentTreeNode>[..._treeNodes];

    while (stack.isNotEmpty) {
      final node = stack.removeLast();
      node.isVisible = true;
      stack.addAll(node.children);
    }
  }

  /// 过滤节点
  bool _filterNodes(List<DepartmentTreeNode> nodes, String searchQuery) {
    bool hasVisibleChild = false;

    for (final node in nodes) {
      // 检查当前节点是否匹配搜索条件
      final currentNodeMatches = node.department.departmentName.toLowerCase().contains(searchQuery);

      // 递归检查子节点
      final hasMatchingDescendant =
          node.children.isNotEmpty ? _filterNodes(node.children, searchQuery) : false;

      // 设置节点的可见性
      node.isVisible = currentNodeMatches || hasMatchingDescendant;

      // 如果当前节点或其子节点匹配，则展开当前节点
      if (node.isVisible) {
        if (currentNodeMatches || hasMatchingDescendant) {
          node.isExpanded = true;
        }
        hasVisibleChild = true;
      }
    }

    return hasVisibleChild;
  }

  /// 处理节点点击事件（仅影响高亮显示，不影响复选框状态）
  void _handleNodeTap(DepartmentTreeNode node) {
    try {
      if (!mounted) return;

      setState(() {
        // 如果有之前选中的节点，取消选中（仅影响高亮显示）
        _clearAllHighlightSelections(_treeNodes);
        // 选中当前节点（仅高亮显示）
        node.isSelected = true;
      });

      // 触发回调
      widget.onNodeTap?.call(node.department);
    } catch (error) {
      debugPrint('处理节点点击时发生错误: $error');
    }
  }

  /// 处理节点展开/折叠
  void _handleNodeToggle(DepartmentTreeNode node) {
    setState(() {
      node.isExpanded = !node.isExpanded;
    });
  }

  /// 处理复选框选择
  void _handleNodeCheckboxChanged(DepartmentTreeNode node, bool? value) {
    try {
      if (!mounted) return; // 检查widget是否仍然挂载

      setState(() {
        final newCheckedState = value ?? false;

        // 设置当前节点的选中状态
        node.isChecked = newCheckedState;
        node.isIndeterminate = false; // 直接操作时清除半选状态

        // 级联选择子节点
        _cascadeCheckChildren(node, newCheckedState);

        // 更新所有父节点的状态
        _updateParentCheckStates();
      });

      // 触发回调，传递当前节点的选中状态
      widget.onNodeSelected?.call(node.department, node.isChecked);
    } catch (error) {
      debugPrint('处理复选框选择时发生错误: $error');
      // 可以选择显示错误提示给用户
    }
  }

  /// 重置所有复选框(避免多次重建)
  void _resetAllNodesCheck() {
    if (!mounted) return;

    setState(() {
      _resetNodesCheckState(_treeNodes);
    });
  }

  /// 递归重置节点的复选框状态
  void _resetNodesCheckState(List<DepartmentTreeNode> nodes) {
    // 使用迭代而非递归，避免栈溢出
    final stack = <DepartmentTreeNode>[...nodes];

    while (stack.isNotEmpty) {
      final node = stack.removeLast();
      node.isChecked = false;
      node.isIndeterminate = false;
      node.clearCache();
      stack.addAll(node.children);
    }
  }

  /// 切换单个节点的复选框状态
  void _toggleNodeCheck(String nodeId, {bool? forceState}) {
    try {
      if (nodeId.isEmpty) {
        debugPrint('警告: 尝试切换空的节点ID');
        return;
      }

      final node = _findNodeById(nodeId);
      if (node == null) {
        debugPrint('警告: 未找到ID为 $nodeId 的节点');
        return;
      }

      if (!mounted) return; // 检查widget是否仍然挂载

      setState(() {
        // 如果指定了强制状态，使用指定状态；否则切换当前状态
        final newCheckedState = forceState ?? !node.isChecked;

        // 设置当前节点的选中状态
        node.isChecked = newCheckedState;
        node.isIndeterminate = false; // 直接操作时清除半选状态

        // 级联选择子节点
        _cascadeCheckChildren(node, newCheckedState);

        // 更新所有父节点的状态
        _updateParentCheckStates();
      });

      // 触发回调，传递当前节点的选中状态
      widget.onNodeSelected?.call(node.department, node.isChecked);
    } catch (error) {
      debugPrint('切换节点复选框状态时发生错误: $error');
    }
  }

  /// 根据ID查找节点（使用缓存映射）
  DepartmentTreeNode? _findNodeById(String nodeId) {
    return _nodeMap[nodeId];
  }

  /// 级联选择子节点）
  void _cascadeCheckChildren(DepartmentTreeNode node, bool checked) {
    // 使用迭代而非递归，避免栈溢出
    final stack = <DepartmentTreeNode>[...node.children];

    while (stack.isNotEmpty) {
      final child = stack.removeLast();
      child.isChecked = checked;
      child.isIndeterminate = false;
      child.clearCache(); // 清除缓存

      // 添加子节点到栈中
      stack.addAll(child.children);
    }
  }

  /// 更新所有父节点的选中状态
  void _updateParentCheckStates() {
    // 从叶子节点向上更新，避免重复计算
    final processedNodes = <String>{};

    for (final node in _nodeMap.values) {
      if (node.isLeaf && !processedNodes.contains(node.department.id)) {
        _updateAncestorStates(node, processedNodes);
      }
    }
  }

  /// 更新祖先节点状态
  void _updateAncestorStates(DepartmentTreeNode leafNode, Set<String> processedNodes) {
    // 向上遍历到根节点
    DepartmentTreeNode? current = leafNode;

    while (current != null) {
      final parentId = current.department.parentId;
      if (parentId?.isNotEmpty != true) break;

      final parent = _nodeMap[parentId!];
      if (parent == null || processedNodes.contains(parent.department.id)) break;

      // 计算父节点状态
      _updateSingleNodeState(parent);
      processedNodes.add(parent.department.id!);

      current = parent;
    }
  }

  /// 更新单个节点的状态
  void _updateSingleNodeState(DepartmentTreeNode node) {
    if (node.children.isEmpty) return;

    int checkedCount = 0;
    int indeterminateCount = 0;

    for (final child in node.children) {
      if (child.isChecked) {
        checkedCount++;
      } else if (child.isIndeterminate) {
        indeterminateCount++;
      }
    }

    // 更新当前节点状态
    if (checkedCount == node.children.length) {
      // 所有子节点都被选中
      node.isChecked = true;
      node.isIndeterminate = false;
    } else if (checkedCount > 0 || indeterminateCount > 0) {
      // 部分子节点被选中或有半选状态
      node.isChecked = false;
      node.isIndeterminate = true;
    } else {
      // 没有子节点被选中
      node.isChecked = false;
      node.isIndeterminate = false;
    }

    node.clearCache(); // 清除缓存
  }

  /// 获取所有被选中的部门
  List<DepartmentModel> _getAllCheckedDepartments() {
    List<DepartmentModel> checkedDepartments = [];
    _collectCheckedDepartments(_treeNodes, checkedDepartments);
    return checkedDepartments;
  }

  /// 递归收集被选中的部门
  void _collectCheckedDepartments(List<DepartmentTreeNode> nodes, List<DepartmentModel> result) {
    for (var node in nodes) {
      if (node.isChecked) {
        result.add(node.department);
      }
      if (node.children.isNotEmpty) {
        _collectCheckedDepartments(node.children, result);
      }
    }
  }

  /// 清除所有高亮选择状态（不影响复选框状态）
  void _clearAllHighlightSelections(List<DepartmentTreeNode> nodes) {
    for (var node in nodes) {
      node.isSelected = false; // 只清除高亮状态，不影响isChecked
      if (node.children.isNotEmpty) {
        _clearAllHighlightSelections(node.children);
      }
    }
  }

  /// 获取所有被选中的部门（公开方法，供外部调用）
  List<DepartmentModel> getAllCheckedDepartments() {
    return _getAllCheckedDepartments();
  }

  /// 手动设置搜索查询（公开方法，供外部调用）
  void setSearchQuery(String? query) {
    if (query != _currentSearchQuery) {
      _currentSearchQuery = query;
      _applySearchFilter();
    }
  }

  /// 清除搜索（公开方法，供外部调用）
  void clearSearch() {
    setSearchQuery(null);
  }

  /// 获取当前搜索查询（公开方法，供外部调用）
  String? getCurrentSearchQuery() {
    return _currentSearchQuery;
  }

  /// 重置所有复选框状态（公开方法，供外部调用）
  void resetAllNodesCheck() {
    _resetAllNodesCheck();
  }

  /// 切换单个节点的复选框状态（公开方法，供外部调用）
  void toggleNodeCheck(String nodeId, {bool? forceState}) {
    _toggleNodeCheck(nodeId, forceState: forceState);
  }

  /// 设置单个节点为选中状态（公开方法，供外部调用）
  void checkNode(String nodeId) {
    _toggleNodeCheck(nodeId, forceState: true);
  }

  /// 设置单个节点为未选中状态（公开方法，供外部调用）
  void uncheckNode(String nodeId) {
    _toggleNodeCheck(nodeId, forceState: false);
  }

  @override
  Widget build(BuildContext context) {
    return _buildContent(context);
  }

  /// 构建内容区域，处理不同状态
  Widget _buildContent(BuildContext context) {
    // 错误状态
    if (_errorMessage != null) {
      return Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(Icons.error_outline, size: 48, color: Theme.of(context).colorScheme.error),
            const SizedBox(height: 16),
            Text(
              _errorMessage!,
              style: Theme.of(
                context,
              ).textTheme.bodyMedium?.copyWith(color: Theme.of(context).colorScheme.error),
              textAlign: TextAlign.center,
            ),
            const SizedBox(height: 16),
            ElevatedButton(
              onPressed: _loadDepartmentData,
              child: const Text(_DepartmentTreeConstants.retryButtonText),
            ),
          ],
        ),
      );
    }

    // 加载状态
    if (_isLoading) {
      return Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            CircularProgressIndicator(),
            SizedBox(height: 16),
            Text(
              _DepartmentTreeConstants.loadingMessage,
              style: Theme.of(context).textTheme.labelMedium,
            ),
          ],
        ),
      );
    }

    // 空数据状态
    if (_list.isEmpty) {
      return const Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(Icons.folder_open, size: 48, color: Colors.grey),
            SizedBox(height: 16),
            Text(_DepartmentTreeConstants.emptyDataMessage, style: TextStyle(color: Colors.grey)),
          ],
        ),
      );
    }

    // 正常内容
    return _buildTreeContent(context);
  }

  /// 构建树内容，处理搜索无结果的情况
  Widget _buildTreeContent(BuildContext context) {
    final treeWidgets = _buildTreeNodes(_treeNodes);

    // 检查是否有搜索条件且无搜索结果
    if (_currentSearchQuery != null &&
        _currentSearchQuery!.trim().isNotEmpty &&
        treeWidgets.isEmpty) {
      return Center(
        child: Text(
          _DepartmentTreeConstants.noSearchResultMessage,
          style: Theme.of(context).textTheme.labelMedium,
        ),
      );
    }

    return ListView(padding: DepartmentTreeStyles.listViewPadding, children: treeWidgets);
  }

  /// 构建树节点列表
  List<Widget> _buildTreeNodes(List<DepartmentTreeNode> nodes) {
    List<Widget> widgets = [];

    for (var node in nodes) {
      // 只构建可见的节点
      if (node.isVisible) {
        widgets.add(_buildTreeNodeWidget(node));

        // 如果节点展开且有子节点，递归构建子节点
        if (node.isExpanded && node.children.isNotEmpty) {
          widgets.addAll(_buildTreeNodes(node.children));
        }
      }
    }

    return widgets;
  }

  /// 构建单个树节点组件（重构版本）
  Widget _buildTreeNodeWidget(DepartmentTreeNode node) {
    return Container(
      margin: DepartmentTreeStyles.nodeContainerMargin(node.level),
      child: Material(
        color: DepartmentTreeStyles.nodeMaterialColor(context, node.isSelected),
        borderRadius: DepartmentTreeStyles.nodeBorderRadius,
        child: InkWell(
          onTap: () => _handleNodeTap(node),
          hoverColor: DepartmentTreeStyles.nodeHoverColor(context),
          splashColor: DepartmentTreeStyles.nodeSplashColor(context),
          borderRadius: DepartmentTreeStyles.nodeBorderRadius,
          child: Container(
            height: DepartmentTreeStyles.nodeHeight,
            padding: DepartmentTreeStyles.nodeContainerPadding,
            child: Row(
              children: [
                _buildExpandIcon(node),
                DepartmentTreeStyles.iconCheckboxSpacing,
                if (widget.showCheckbox) ..._buildCheckboxSection(node),
                _buildNodeText(node),
              ],
            ),
          ),
        ),
      ),
    );
  }

  /// 构建展开/折叠图标
  Widget _buildExpandIcon(DepartmentTreeNode node) {
    return SizedBox(
      width: DepartmentTreeStyles.expandIconSize.width,
      height: DepartmentTreeStyles.expandIconSize.height,
      child:
          node.children.isNotEmpty
              ? GestureDetector(
                onTap: () => _handleNodeToggle(node),
                child: Center(
                  child: AnimatedRotation(
                    turns: node.isExpanded ? 0 : -0.25,
                    duration: DepartmentTreeStyles.expandAnimationDuration,
                    child: Icon(
                      DepartmentTreeStyles.expandIcon,
                      size: DepartmentTreeStyles.expandIconSizeValue,
                      color: DepartmentTreeStyles.expandIconColor(context),
                    ),
                  ),
                ),
              )
              : null, // 叶子节点不显示图标，但保持占位空间
    );
  }

  /// 构建复选框区域
  List<Widget> _buildCheckboxSection(DepartmentTreeNode node) {
    return [
      SizedBox(
        height: DepartmentTreeStyles.checkboxHeight,
        child: Checkbox(
          value: node.isIndeterminate ? null : node.isChecked,
          tristate: true, // 启用三态支持
          onChanged: (value) => _handleNodeCheckboxChanged(node, value),
        ),
      ),
      DepartmentTreeStyles.checkboxTextSpacing,
    ];
  }

  /// 构建节点文本
  Widget _buildNodeText(DepartmentTreeNode node) {
    return Expanded(
      child: Align(
        alignment: DepartmentTreeStyles.textAlignment,
        child: Text(
          node.department.departmentName,
          style: DepartmentTreeStyles.departmentNameTextStyle(context, node.isSelected),
          overflow: DepartmentTreeStyles.textOverflow,
          maxLines: DepartmentTreeStyles.textMaxLines,
        ),
      ),
    );
  }
}
