// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'app_table_column_type_telephone.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

AppTableColumnTypeTelephone _$AppTableColumnTypeTelephoneFromJson(
  Map<String, dynamic> json,
) =>
    AppTableColumnTypeTelephone(
        defaultValue: json['defaultValue'] ?? '',
        columnDesc: json['columnDesc'] as String? ?? '',
        telephoneType:
            $enumDecodeNullable(
              _$TelephoneEnumEnumMap,
              json['telephoneType'],
            ) ??
            TelephoneEnum.mobile,
      )
      ..isSaveRequired = json['isSaveRequired'] as bool
      ..isSubmitRequired = json['isSubmitRequired'] as bool
      ..typeCode = $enumDecode(_$ColumnTypeEnumEnumMap, json['typeCode']);

Map<String, dynamic> _$AppTableColumnTypeTelephoneToJson(
  AppTableColumnTypeTelephone instance,
) => <String, dynamic>{
  'defaultValue': instance.defaultValue,
  'isSaveRequired': instance.isSaveRequired,
  'isSubmitRequired': instance.isSubmitRequired,
  'columnDesc': instance.columnDesc,
  'typeCode': _$ColumnTypeEnumEnumMap[instance.typeCode]!,
  'telephoneType': _$TelephoneEnumEnumMap[instance.telephoneType]!,
};

const _$TelephoneEnumEnumMap = {
  TelephoneEnum.mobile: 'mobile',
  TelephoneEnum.landline: 'landline',
};

const _$ColumnTypeEnumEnumMap = {
  ColumnTypeEnum.text: 'text',
  ColumnTypeEnum.singleSelect: 'singleSelect',
  ColumnTypeEnum.multipleSelect: 'multipleSelect',
  ColumnTypeEnum.number: 'number',
  ColumnTypeEnum.date: 'date',
  ColumnTypeEnum.percentage: 'percentage',
  ColumnTypeEnum.checkbox: 'checkbox',
  ColumnTypeEnum.hyperlink: 'hyperlink',
  ColumnTypeEnum.parentRecord: 'parentRecord',
  ColumnTypeEnum.telephone: 'telephone',
  ColumnTypeEnum.currency: 'currency',
};
