// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'department_model.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

DepartmentModel _$DepartmentModelFromJson(Map<String, dynamic> json) => DepartmentModel(
  parentIdList: (json['ParentIdList'] as List<dynamic>?)?.map((e) => e as String).toList() ?? [],
  parentList:
      (json['ParentList'] as List<dynamic>?)
          ?.map((e) => DepartmentModel.fromJson(e as Map<String, dynamic>))
          .toList() ??
      [],
  departmentHeadIdList: json['DepartmentHeadIdList'] as List<dynamic>? ?? [],
  departmentHeadList: json['DepartmentHeadList'] as List<dynamic>? ?? [],
  id: json['Id'] as String?,
  departmentName: json['DepartmentName'] as String? ?? '',
  parentId: json['ParentId'] as String?,
  parentName: json['ParentName'] as String? ?? '',
  secondarySuperiorIds: json['SecondarySuperiorIds'] as String?,
  departmentHead: json['DepartmentHead'] as String?,
  departmentHrbp: json['DepartmentHRBP'],
  description: json['Description'] as String? ?? '',
);

Map<String, dynamic> _$DepartmentModelToJson(DepartmentModel instance) => <String, dynamic>{
  'ParentIdList': instance.parentIdList,
  'ParentList': instance.parentList,
  'DepartmentHeadIdList': instance.departmentHeadIdList,
  'DepartmentHeadList': instance.departmentHeadList,
  'Id': instance.id,
  'DepartmentName': instance.departmentName,
  'ParentId': instance.parentId,
  'ParentName': instance.parentName,
  'SecondarySuperiorIds': instance.secondarySuperiorIds,
  'DepartmentHead': instance.departmentHead,
  'DepartmentHRBP': instance.departmentHrbp,
  'Description': instance.description,
};
