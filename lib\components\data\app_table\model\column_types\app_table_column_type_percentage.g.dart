// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'app_table_column_type_percentage.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

AppTableColumnTypePercentage _$AppTableColumnTypePercentageFromJson(
  Map<String, dynamic> json,
) =>
    AppTableColumnTypePercentage(
        defaultValue: json['defaultValue'] ?? 0,
        columnDesc: json['columnDesc'] as String? ?? '',
        negative: json['negative'] as bool,
        format: json['format'] as String,
        applyFormatOnInit: json['applyFormatOnInit'] as bool,
        allowFirstDot: json['allowFirstDot'] as bool,
        locale: json['locale'] as String?,
        precision: (json['precision'] as num?)?.toInt() ?? 0,
      )
      ..isSaveRequired = json['isSaveRequired'] as bool
      ..isSubmitRequired = json['isSubmitRequired'] as bool
      ..typeCode = $enumDecode(_$ColumnTypeEnumEnumMap, json['typeCode']);

Map<String, dynamic> _$AppTableColumnTypePercentageToJson(
  AppTableColumnTypePercentage instance,
) => <String, dynamic>{
  'defaultValue': instance.defaultValue,
  'isSaveRequired': instance.isSaveRequired,
  'isSubmitRequired': instance.isSubmitRequired,
  'columnDesc': instance.columnDesc,
  'typeCode': _$ColumnTypeEnumEnumMap[instance.typeCode]!,
  'negative': instance.negative,
  'allowFirstDot': instance.allowFirstDot,
  'locale': instance.locale,
  'format': instance.format,
  'applyFormatOnInit': instance.applyFormatOnInit,
  'precision': instance.precision,
};

const _$ColumnTypeEnumEnumMap = {
  ColumnTypeEnum.text: 'text',
  ColumnTypeEnum.singleSelect: 'singleSelect',
  ColumnTypeEnum.multipleSelect: 'multipleSelect',
  ColumnTypeEnum.number: 'number',
  ColumnTypeEnum.date: 'date',
  ColumnTypeEnum.percentage: 'percentage',
  ColumnTypeEnum.checkbox: 'checkbox',
  ColumnTypeEnum.hyperlink: 'hyperlink',
  ColumnTypeEnum.parentRecord: 'parentRecord',
  ColumnTypeEnum.telephone: 'telephone',
  ColumnTypeEnum.currency: 'currency',
};
