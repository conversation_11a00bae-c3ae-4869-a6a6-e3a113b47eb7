import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:octasync_client/components/data/app_table/app_basic_text_field.dart';
import 'package:octasync_client/components/data/app_table/app_number.dart';
import 'package:octasync_client/components/data/app_table/enums/cell_state_enum.dart';
import 'package:octasync_client/components/data/app_table/helper/app_table_common.dart';
import 'package:octasync_client/components/data/app_table/helper/app_table_general_helper.dart';
import 'package:octasync_client/components/data/app_table/model/app_table_column.dart';
import 'package:octasync_client/components/data/app_table/model/app_table_column_type_with_number_format.dart';
import 'package:octasync_client/components/data/app_table/model/column_types/app_table_column_type_currency.dart';
import 'package:octasync_client/components/data/app_table/model/column_types/app_table_column_type_number.dart';
import 'package:octasync_client/components/data/app_table/model/column_types/app_table_column_type_percentage.dart';
import 'package:octasync_client/components/data/app_table/model/column_types/app_table_column_type_telephone.dart';
import 'package:intl/intl.dart';

class TelephoneCell extends StatefulWidget {
  const TelephoneCell({
    super.key,
    this.state = CellStateEnum.normal,
    required this.column,
    required this.rowData,
    required this.rowIdx,
    required this.columnIdx,
    required this.rowId,
    required this.columnId,
  });

  final CellStateEnum state;

  final AppTableColumn column;

  final Map<String, dynamic> rowData;

  final int rowIdx;

  final int columnIdx;
  final String rowId;
  final String columnId;

  @override
  State<TelephoneCell> createState() => _NumberCellState();
}

class _NumberCellState extends State<TelephoneCell> {
  late AppTableColumn column;
  late AppTableColumnTypeTelephone columnObj;
  late Map<String, dynamic> rowData;
  late String rowId;
  late String columnId;

  var outlineInputBorder = OutlineInputBorder(borderSide: BorderSide(color: Colors.grey, width: 1));

  @override
  void initState() {
    // TODO: implement initState
    super.initState();
    column = widget.column;
    columnObj = column.type as AppTableColumnTypeTelephone;
    rowData = widget.rowData;
    rowId = widget.rowId;
    columnId = widget.columnId;
  }

  @override
  Widget build(BuildContext context) {
    var value = rowData[column.field];
    var state = widget.state;

    /// 手机或者固定电话
    var telephoneType = columnObj.telephoneType;

    if (state == CellStateEnum.edit) {
      return AppBasicTextField(
        initialValue: value,
        onChanged: (value) {
          rowData[column.field] = value;
          setState(() {});
        },
        decoration: InputDecoration(
          border: outlineInputBorder,
          contentPadding: EdgeInsets.symmetric(horizontal: 10),
          hintText: '请输入',
        ),
      );
    } else {
      return Text('${value}');
    }
  }
}
