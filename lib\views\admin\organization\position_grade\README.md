# 职级管理弹窗使用指南

## 概述

本模块实现了基于 `DialogTypeEmun` 枚举的职级管理弹窗，支持创建和编辑两种模式。

## 枚举定义

```dart
/// 弹窗类型
enum DialogTypeEmun { create, edit }
```

## 核心组件

### 1. GradeDialog 组件

职级弹窗组件，支持创建和编辑功能。

#### 主要属性
- `child`: 触发弹窗的子组件
- `onSuccess`: 提交成功后的回调函数

#### 核心方法

```dart
/// 显示弹窗
void showGradeDialog(
  BuildContext context, {
  DialogTypeEmun type = DialogTypeEmun.create,  // 弹窗类型
  Map<String, dynamic>? editData,              // 编辑时的数据
})
```

### 2. 使用示例

#### 创建模式
```dart
_gradeDialogStateKey.currentState?.showGradeDialog(
  context,
  type: DialogTypeEmun.create,
);
```

#### 编辑模式
```dart
_gradeDialogStateKey.currentState?.showGradeDialog(
  context,
  type: DialogTypeEmun.edit,
  editData: rowData, // 传入要编辑的数据
);
```

## 功能差异

### 创建模式 (DialogTypeEmun.create)
- 弹窗标题：`添加职级`
- 按钮文本：`确定`
- 显示"继续新建下一条"选项
- 提交后可选择继续添加或关闭弹窗
- 调用 `PositionsApi.add()` 方法

### 编辑模式 (DialogTypeEmun.edit)
- 弹窗标题：`编辑职级`
- 按钮文本：`保存`
- 不显示"继续新建下一条"选项
- 表单预填充现有数据
- 提交后直接关闭弹窗
- 调用 `PositionsApi.edit()` 方法

## 数据流程

### 创建流程
1. 用户点击"添加职级"按钮
2. 调用 `showGradeDialog()` 并传入 `DialogTypeEmun.create`
3. 弹窗显示空表单
4. 用户填写信息并提交
5. 调用 API 创建新记录
6. 根据"继续新建"选项决定是否关闭弹窗

### 编辑流程
1. 用户点击表格行的"编辑"按钮
2. 调用 `showGradeDialog()` 并传入 `DialogTypeEmun.edit` 和行数据
3. 弹窗显示预填充的表单
4. 用户修改信息并提交
5. 调用 API 更新记录
6. 弹窗关闭并刷新列表

## 最佳实践

### 1. 状态管理
- 使用 `GlobalKey<GradeDialogState>` 访问弹窗状态
- 通过 `onSuccess` 回调刷新列表数据

### 2. 数据验证
- 使用 `Form` 和 `FormField` 进行表单验证
- 必填字段使用 `validator` 函数

### 3. 错误处理
- API 调用使用 try-catch 包装
- 显示相应的成功/失败提示信息

### 4. 用户体验
- 提交时显示加载状态
- 编辑模式不显示不必要的选项
- 根据操作类型显示不同的按钮文本

## 扩展建议

### 1. 添加更多弹窗类型
```dart
enum DialogTypeEmun { 
  create,    // 创建
  edit,      // 编辑
  view,      // 查看（只读）
  copy,      // 复制
}
```

### 2. 支持批量操作
```dart
enum DialogTypeEmun { 
  create, 
  edit, 
  batchEdit,  // 批量编辑
}
```

### 3. 添加权限控制
```dart
void showGradeDialog(
  BuildContext context, {
  DialogTypeEmun type = DialogTypeEmun.create,
  Map<String, dynamic>? editData,
  bool canEdit = true,  // 是否可编辑
  bool canDelete = true, // 是否可删除
})
```

## 注意事项

1. **数据一致性**: 编辑时确保传入的数据包含必要的 ID 字段
2. **内存管理**: 及时释放 TextEditingController 和其他资源
3. **异步操作**: 正确处理异步 API 调用和状态更新
4. **用户反馈**: 提供清晰的操作反馈和错误提示
