import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:intl/intl.dart' as intl;

class AppBasicTextField extends StatefulWidget {
  final String? initialValue; // 初始值
  final ValueChanged<String>? onChanged; //返回原始输入值
  final String Function(dynamic value)? formatOnFocus; // 获取焦点时的格式
  final String Function(dynamic value)? formatOnBlur; // 失去焦点时的格式
  final Function(String value)? onParsedChanged; // 返回解析后的值
  // 解析输入文本为所需要的值（例如：如果你需要转为数字，那就提供转为数字的方法；）
  final dynamic Function(String text)? parser;
  final TextEditingController? controller;
  final FocusNode? focusNode;
  final InputDecoration? decoration;
  final TextInputType? keyboardType;
  final List<TextInputFormatter>? inputFormatters;
  final TextInputAction? textInputAction;
  final TextCapitalization textCapitalization;
  final TextStyle? style;
  final StrutStyle? strutStyle;
  final TextAlign textAlign;
  final TextAlignVertical? textAlignVertical;
  final TextDirection? textDirection;
  final bool autofocus;
  final bool readOnly;
  final bool? showCursor;
  final String obscuringCharacter;
  final bool obscureText;
  final bool autocorrect;
  final SmartDashesType? smartDashesType;
  final SmartQuotesType? smartQuotesType;
  final bool enableSuggestions;
  final MaxLengthEnforcement? maxLengthEnforcement;
  final int? maxLines;
  final int? minLines;
  final bool expands;
  final bool? enabled;
  final double cursorWidth;
  final double? cursorHeight;
  final Radius? cursorRadius;
  final Color? cursorColor;
  final Brightness? keyboardAppearance;
  final EdgeInsets scrollPadding;
  final bool? enableInteractiveSelection;
  final TextSelectionControls? selectionControls;
  final GestureTapCallback? onTap;
  final MouseCursor? mouseCursor;
  final InputCounterWidgetBuilder? buildCounter;
  final ScrollPhysics? scrollPhysics;
  final ScrollController? scrollController;
  final String? Function(String?)? validator;

  // /// normalFormatString 鼠标失去焦点时需要的格式化文本字符（在该组件中专门用来监听变化）
  // final String? normalFormatString;

  const AppBasicTextField({
    super.key,
    this.initialValue,
    this.onChanged,
    this.formatOnFocus,
    this.formatOnBlur,
    this.onParsedChanged,
    this.parser,
    this.controller,
    this.focusNode,
    this.decoration = const InputDecoration(),
    this.keyboardType,
    this.inputFormatters,
    this.textInputAction,
    this.textCapitalization = TextCapitalization.none,
    this.style,
    this.strutStyle,
    this.textAlign = TextAlign.start,
    this.textAlignVertical,
    this.textDirection = TextDirection.ltr,
    this.autofocus = false,
    this.readOnly = false,
    this.showCursor,
    this.obscuringCharacter = '•',
    this.obscureText = false,
    this.autocorrect = true,
    this.smartDashesType,
    this.smartQuotesType = SmartQuotesType.enabled,
    this.enableSuggestions = true,
    this.maxLengthEnforcement,
    this.maxLines = 1,
    this.minLines,
    this.expands = false,
    this.enabled,
    this.cursorWidth = 2.0,
    this.cursorHeight,
    this.cursorRadius,
    this.cursorColor,
    this.keyboardAppearance,
    this.scrollPadding = const EdgeInsets.all(20.0),
    this.enableInteractiveSelection = true,
    this.selectionControls,
    this.onTap,
    this.mouseCursor,
    this.buildCounter,
    this.scrollPhysics,
    this.scrollController,
    this.validator,
    // this.normalFormatString,
  });

  @override
  State<AppBasicTextField> createState() => _AppBasicTextFieldState();
}

class _AppBasicTextFieldState extends State<AppBasicTextField> {
  late TextEditingController _controller;
  late FocusNode _focusNode;
  dynamic _parsedValue;
  bool _hasFocus = false;

  @override
  void initState() {
    super.initState();

    // 初始化控制器
    _controller = widget.controller ?? TextEditingController();
    _focusNode = widget.focusNode ?? FocusNode();

    // 设置初始值
    if (widget.initialValue != null) {
      _parseValue(widget.initialValue!);
      _updateControllerText();
    }

    // 监听焦点变化
    _focusNode.addListener(_handleFocusChange);
  }

  @override
  void didUpdateWidget(covariant AppBasicTextField oldWidget) {
    super.didUpdateWidget(oldWidget);

    // 当 initialValue 发生变化时，更新文本控制器
    // _parsedValue != _getParsedValue(widget.initialValue ?? '') 表示内部修改
    if (oldWidget.initialValue != widget.initialValue &&
        _parsedValue != _getParsedValue(widget.initialValue ?? '')) {
      if (widget.initialValue != null) {
        _parseValue(widget.initialValue!);
        _updateControllerText();
      } else {
        _parsedValue = null;
        _controller.text = '';
      }
    }
  }

  void _updateControllerText() {
    if (widget.formatOnBlur != null && _parsedValue != null) {
      _controller.text = widget.formatOnBlur!(_parsedValue);
    } else {
      _controller.text = widget.initialValue!;
    }
  }

  @override
  void didChangeDependencies() {
    super.didChangeDependencies();
    // 确保初始状态正确
    if (_focusNode.hasFocus != _hasFocus) {
      _handleFocusChange();
    }
  }

  @override
  void dispose() {
    _focusNode.removeListener(_handleFocusChange);
    if (widget.controller == null) {
      _controller.dispose();
    }
    if (widget.focusNode == null) {
      _focusNode.dispose();
    }
    super.dispose();
  }

  void _handleFocusChange() {
    setState(() {
      _hasFocus = _focusNode.hasFocus;
    });

    if (_hasFocus) {
      // 获取焦点时显示原始值
      if (widget.formatOnFocus != null && _parsedValue != null) {
        _controller.text = widget.formatOnFocus!(_parsedValue);
      } else {
        // 默认显示解析后的原始值
        _controller.text = _parsedValue?.toString() ?? '';
      }
      // 将光标移到末尾
      _controller.selection = TextSelection.fromPosition(
        TextPosition(offset: _controller.text.length),
      );
    } else {
      // 失去焦点时显示格式化值
      if (widget.formatOnBlur != null) {
        if (_parsedValue != null) {
          _controller.text = widget.formatOnBlur!(_parsedValue);
        } else {
          _controller.text = '';
        }
      }
    }
  }

  void _parseValue(String text) {
    _parsedValue = _getParsedValue(text);
  }

  dynamic _getParsedValue(String text) {
    dynamic result;
    if (widget.parser != null) {
      try {
        result = widget.parser!(text);
      } catch (e) {
        result = text;
      }
    } else {
      result = text;
    }

    return result;
  }

  void _handleChanged(String text) {
    _parseValue(text);

    if (widget.onChanged != null) {
      widget.onChanged!(text);
    }

    //  && _parsedValue != null
    if (widget.onParsedChanged != null) {
      widget.onParsedChanged!(_parsedValue?.toString() ?? '');
    }
  }

  @override
  Widget build(BuildContext context) {
    return LayoutBuilder(
      builder: (context, constraints) {
        final maxWidth =
            constraints.hasBoundedWidth
                ? constraints.maxWidth
                : double.infinity;

        return IntrinsicWidth(
          child: ConstrainedBox(
            constraints: BoxConstraints(
              minWidth: 0,
              maxWidth: maxWidth,
              minHeight: 38,
            ),
            child: Container(
              // color: Colors.red,
              width: maxWidth,
              // height: 38,
              child: TextField(
                controller: _controller,
                focusNode: _focusNode,
                decoration:
                    widget.decoration?.copyWith(
                      isDense: true, // 重要：减少默认padding
                      contentPadding: const EdgeInsets.symmetric(
                        horizontal: 10,
                        vertical: 11, // 调整垂直padding以实现居中
                      ),
                    ) ??
                    const InputDecoration(
                      isDense: true,
                      contentPadding: EdgeInsets.symmetric(
                        horizontal: 10,
                        vertical: 11,
                      ),
                    ),
                keyboardType: widget.keyboardType,
                inputFormatters: widget.inputFormatters,
                textInputAction: widget.textInputAction,
                textCapitalization: widget.textCapitalization,
                style: widget.style,
                strutStyle: widget.strutStyle,
                textAlign: widget.textAlign,
                textAlignVertical:
                    widget.textAlignVertical ?? TextAlignVertical.center,
                textDirection: widget.textDirection,
                autofocus: widget.autofocus,
                readOnly: widget.readOnly,
                showCursor: widget.showCursor,
                obscuringCharacter: widget.obscuringCharacter,
                obscureText: widget.obscureText,
                autocorrect: widget.autocorrect,
                smartDashesType: widget.smartDashesType,
                smartQuotesType: widget.smartQuotesType,
                enableSuggestions: widget.enableSuggestions,
                maxLengthEnforcement: widget.maxLengthEnforcement,
                maxLines: widget.maxLines,
                minLines: widget.minLines,
                expands: widget.expands,
                enabled: widget.enabled,
                cursorWidth: widget.cursorWidth,
                cursorHeight: widget.cursorHeight,
                cursorRadius: widget.cursorRadius,
                cursorColor: widget.cursorColor,
                keyboardAppearance: widget.keyboardAppearance,
                scrollPadding: widget.scrollPadding,
                enableInteractiveSelection: widget.enableInteractiveSelection,
                selectionControls: widget.selectionControls,
                onTap: widget.onTap,
                mouseCursor: widget.mouseCursor,
                buildCounter: widget.buildCounter,
                scrollPhysics: widget.scrollPhysics,
                scrollController: widget.scrollController,
                onChanged: _handleChanged,
              ),
            ),
          ),
        );
      },
    );
  }
}
