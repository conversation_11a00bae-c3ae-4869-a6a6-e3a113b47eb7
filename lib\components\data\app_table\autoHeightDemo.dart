import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';

class AutoHeightdemo extends StatefulWidget {
  const AutoHeightdemo({super.key});

  @override
  State<AutoHeightdemo> createState() => _AutoHeightdemoState();
}

class _AutoHeightdemoState extends State<AutoHeightdemo> {
  List<String> _items = [
    'Item 1',
    'Item 2',
    'Item 3',
    'Item 4',
    'Item 5',
    '111',
    '2222',
    '3333',
  ];

  // 重新排序列表
  void _onReorder(int oldIndex, int newIndex) {
    setState(() {
      if (newIndex > oldIndex) {
        newIndex -= 1;
      }
      final item = _items.removeAt(oldIndex);
      _items.insert(newIndex, item);
    });
  }

  @override
  Widget build(BuildContext context) {
    return Container(
      constraints: BoxConstraints(
        maxHeight: 200, // 最大高度为200
      ),
      child: SingleChildScrollView(
        // 允许内容超出时滚动
        child: ConstrainedBox(
          constraints: BoxConstraints(maxHeight: 200),
          child: ReorderableListView(
            shrinkWrap: true, // 设置为 true 以确保列表适应内容高度
            onReorder: _onReorder, // 处理排序事件
            children:
                _items
                    .map(
                      (item) =>
                          ListTile(key: ValueKey(item), title: Text(item)),
                    )
                    .toList(),
          ),
        ),
      ),
    );
  }
}
