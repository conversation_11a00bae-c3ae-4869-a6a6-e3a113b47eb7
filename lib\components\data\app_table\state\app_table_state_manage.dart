import 'dart:async';
import 'dart:convert';

import 'package:collection/collection.dart';
import 'package:flutter/gestures.dart';
import 'package:flutter/material.dart';
import 'package:octasync_client/components/data/app_table/app_table_column_type.dart';
import 'package:octasync_client/components/data/app_table/enums/column_type_enum.dart';
import 'package:octasync_client/components/data/app_table/enums/select_enum.dart';
import 'package:octasync_client/components/data/app_table/enums/table_checked_enum.dart';
import 'package:octasync_client/components/data/app_table/helper/app_table_common.dart';
import 'package:octasync_client/components/data/app_table/helper/app_table_general_helper.dart';
import 'package:octasync_client/components/data/app_table/model/app_table_column_type_with_number_format.dart';
import 'package:octasync_client/components/data/app_table/model/app_table_column_type_with_request_field.dart';
import 'package:octasync_client/components/data/app_table/model/column_types/app_table_column_type_checkbox.dart';
import 'package:octasync_client/components/data/app_table/model/column_types/app_table_column_type_currency.dart';
import 'package:octasync_client/components/data/app_table/model/column_types/app_table_column_type_date.dart';
import 'package:octasync_client/components/data/app_table/model/column_types/app_table_column_type_number.dart';
import 'package:octasync_client/components/data/app_table/model/column_types/app_table_column_type_percentage.dart';
import 'package:octasync_client/components/data/app_table/model/column_types/app_table_column_type_single_select.dart';
import 'package:octasync_client/components/data/app_table/model/column_types/app_table_column_type_telephone.dart';
import 'package:octasync_client/components/data/app_table/model/column_types/app_table_column_type_text.dart';
import 'package:octasync_client/components/data/app_table/model/general_option.dart';
import 'package:intl/intl.dart';
import 'package:uuid/uuid.dart';
import 'package:octasync_client/components/data/app_table/model/app_table_column.dart';
import 'package:octasync_client/components/data/app_table/model/app_table_row_data.dart';

/// 列坐标信息
class ColumnPosition {
  final double left; //开始位置
  final double right; //结束位置（开始位置加上宽度）

  ColumnPosition({required this.left, required this.right});
}

class TreeTableConfig {
  /// ID字段名
  final String idField;

  /// 父节点ID字段名
  final String parentIdField;

  /// 空的父节点ID值（用于标识根节点）
  final String? emptyParentValue;

  const TreeTableConfig({
    this.idField = 'id', // 默认使用 'id' 作为 ID 字段
    this.parentIdField = 'parentId', // 默认使用 'parentId' 作为父节点 ID 字段
    this.emptyParentValue, // 默认使用空字符串作为根节点标识
  });
}

/// 鼠标位置（相对于表格）
enum MousePositionOfTable {
  leftOutside, //鼠标在表格左侧外边
  rightOutside, //鼠标在表格右侧外边
  topOutside,
  bottomOutside,
  inside, // 鼠标在表格内
}

/// 鼠标位置处于什么区域（表格分为冻结区域（左侧）、非冻结区域（右侧））
enum AreaOfMouse {
  //冻结区域
  frozen,
  //非冻结区域
  notFrozen,
  //未知区域
  none,
}

class AppTableStateManage with ChangeNotifier {
  int temp = 100;

  add() {
    temp++;
    notifyListeners();
  }

  final Uuid _uuid = Uuid();

  TableCheckedEnum checkType;

  /// 加载中
  bool _isLoading = false;
  bool get isLoading => _isLoading;

  /// 加载更多中
  bool _loadingMore = false;
  bool get loadingMore => _loadingMore;

  /// 是否还有更多数据
  bool _hasMore = true;
  bool get hasMore => _hasMore;

  Offset? tbPosition = null;
  Size? tbSize = null;
  double tbViewportWidth = -1;
  double tbViewportHeight = -1;

  int _columnsChanged = 0; // 用于标记columns是否发生变化，便于刷新UI
  int _rowsChanged = 0;
  List<AppTableColumn> _columns;
  // final List<GlobalKey> _columnsKeys = []; //暂时用不到（改为计算实现）
  // final List<Map<String, dynamic>> _rows;

  // 现有的属性保持不变
  List<AppTableRowData> _flattenedRows = []; // 扁平化后的数据（用于显示）
  List<AppTableRowData> _allRows = []; // 所有数据（包括隐藏的）

  final ScrollController horizontalScrollController;
  final ScrollController verticalScrollController;
  Color tableBgColor; // 表格背景色
  Color borderColor; // 边框颜色
  Color joinLineColor; //树结构连接线颜色
  double rowHeight = 50; // 行高
  double indentSize = 24; //缩进
  double headerHeight = 50; // 表头高度
  double borderWidth = 1; // 边框宽度
  double indexColumnWidth = 50; // 索引列宽
  double tableWrapperPadding = 8; // 表格外部容器的padding
  int emptyRowTotal = 5; //表格底部空白行（主要用于留白，尽量不要用，用listviewPddingBottom代替）
  double listviewPddingBottom = 150; //表格底部留白
  int? maxLevel; //树结构支持最大层级
  EdgeInsetsGeometry headerCellPadding;
  EdgeInsetsGeometry cellPadding;
  final bool defaultExpandAll;

  final bool showAddRowButton;
  final bool showAddColumnButton;

  final double frozenAreaMaxWidth;

  final String uniqueId;
  final TreeTableConfig? treeConfig;

  List<int> selectedColumnIndexs = []; //选中列头索引（应该是连续的）

  int get columnsChanged => _columnsChanged;
  int get rowsChanged => _rowsChanged;

  ///所有列
  List<AppTableColumn> get columns => _columns;

  /// 冻结列
  List<AppTableColumn> get columnsFrozen =>
      _columns.where((c) => c.frozen == AppTableColumnFrozen.start).toList();
  // List<GlobalKey> get columnsKeys => _columnsKeys;
  // List<Map<String, dynamic>> get rows => _rows;
  // 修改获取行数据的 getter
  List<Map<String, dynamic>> get rows => _flattenedRows.map((e) => e.data).toList();

  List<AppTableRowData> get allRows => _allRows.map((e) => e).toList();

  /// 选中列头的总宽度
  double? get selectedColumnWidth {
    double width = 0;
    if (selectedColumnIndexs.isNotEmpty) {
      width = getWidthByColumns(selectedColumnIndexs); //选中列头宽度
    }
    return width;
  }

  /// 只包括数据列的宽度
  double get tableBodyWidth =>
      _columns.isEmpty
          ? 0
          : _columns.map((col) => col.width).reduce((total, width) => total + width);

  /// 表格内容所占宽度（包括空白区域）
  double get tableContentWidth =>
      indexColumnWidth + tableBodyWidth + (showAddColumnButton ? headerHeight : 0);

  /// 冻结列（包含索引列）区域宽度
  double get tableWidthOfFrozenArea => _columns
      .where((col) => col.frozen == AppTableColumnFrozen.start)
      .map((col) => col.width)
      .fold(indexColumnWidth, (total, width) => total + width);

  /// 非冻结列区域宽度（表格总宽度 - 冻结区域宽度）
  double get tableWidthOfNotFrozenArea => tableContentWidth - tableWidthOfFrozenArea;

  /// 表格底部空白（非内容区域）区域高度
  double get whiteSpaceHeight =>
      (showAddRowButton ? 1 : 0) * rowHeight +
      emptyRowTotal * rowHeight +
      listviewPddingBottom +
      tableWrapperPadding;

  // body 区域实际高度（包含隐藏的行）——表格行所占高度 + tableWrapperPadding（不包括表头）
  double get tableHeight => _flattenedRows.length * rowHeight + whiteSpaceHeight;

  double get tableBodyHeight => headerHeight + _flattenedRows.length * rowHeight;

  AppTableStateManage({
    // required List<AppTableColumn> columns,
    // required List<Map<String, dynamic>> rows,
    this.checkType = TableCheckedEnum.none,
    required this.horizontalScrollController,
    required this.verticalScrollController,
    this.tableBgColor = Colors.white,
    this.borderColor = Colors.blue,
    this.joinLineColor = Colors.blue,
    this.rowHeight = 50,
    this.indentSize = 24,
    this.headerHeight = 50,
    this.borderWidth = 1,
    this.indexColumnWidth = 50,
    this.tableWrapperPadding = 8,
    this.emptyRowTotal = 5,
    this.listviewPddingBottom = 150,
    this.maxLevel,
    this.headerCellPadding = const EdgeInsets.symmetric(horizontal: 4, vertical: 0),
    this.cellPadding = const EdgeInsets.symmetric(horizontal: 4, vertical: 0),
    this.defaultExpandAll = false,
    this.showAddRowButton = false,
    this.showAddColumnButton = false,
    this.frozenAreaMaxWidth = 1000,
    this.uniqueId = 'id',
    this.treeConfig,
  }) : _columns = [] {
    // _initColumnsKeys();

    // _initColumns();

    // WidgetsBinding.instance.addPostFrameCallback((_) {
    //   setFrozenLinex(tbPosition);
    // });

    // // 将原始数据转换为树形结构
    // _allRows = _convertToTreeData(rows);

    // // 根据 defaultExpandAll 参数初始化显示的行
    // _initializeVisibleRows();
  }

  // /// 初始化，为所有的列添加key
  // void _initColumnsKeys() {
  //   for (int i = 0; i < _columns.length; i++) {
  //     _columnsKeys.add(GlobalKey());
  //   }
  // }

  void setLoading(bool isShow) {
    if (isShow != _isLoading) {
      _isLoading = isShow;
      notifyListeners();
    }
  }

  void setLoadingMore(bool isShow) {
    if (isShow != _loadingMore) {
      _loadingMore = isShow;
      notifyListeners();
    }
  }

  void setHasMore(bool hasMore) {
    if (hasMore != _hasMore) {
      _hasMore = hasMore;
      notifyListeners();
    }
  }

  // void setColumns(List<AppTableColumn> cols) {}

  /// 设置表格基础信息
  void setTableInfo(
    double tableViewportWidth,
    double tableViewportHeight,
    Offset tablePosition,
    Size tableSize,
  ) {
    tbPosition = tablePosition;
    tbSize = tableSize;
    tbViewportWidth = tableViewportWidth;
    tbViewportHeight = tableViewportHeight;
  }

  /// 设置列数据
  void setColumns(List<AppTableColumn> cols) {
    if (cols.isEmpty) return;

    //最后一个索引列
    var lastFrozenIdx = cols.lastIndexWhere((row) => row.frozen == AppTableColumnFrozen.start);
    //如果没有设置冻结列（把第一列当作冻结列）
    if (lastFrozenIdx == -1) {
      lastFrozenIdx = 0;
    }

    //冻结列所占宽度
    double tempFrozenAreaWidth = 0;

    // 初始化时，设置合理的冻结列
    // 确保冻结列总宽小于限制 frozenAreaMaxWidth
    for (var i = 0; i < cols.length; i++) {
      var curr = cols[i];
      if (tempFrozenAreaWidth + curr.width <= frozenAreaMaxWidth && i <= lastFrozenIdx) {
        tempFrozenAreaWidth += curr.width;
        curr.frozen = AppTableColumnFrozen.start;
      } else {
        curr.frozen = AppTableColumnFrozen.none;
      }
    }

    _columns = cols;

    _markColumnsChanged();
    notifyListeners();
  }

  /// 设置行数据
  void setRows(List<AppTableRowData> rows) {
    clearRows();
    _allRows = rows;
    // 根据 defaultExpandAll 参数初始化显示的行
    _initializeVisibleRows();

    _markRowsChanged();
    notifyListeners();
  }

  /// 追加行数据
  void appendRows(List<AppTableRowData> rows) {
    if (rows.isEmpty) return;

    // 将新行添加到 _allRows 中
    _allRows.addAll(rows);

    // 传递旧节点状态，重建树结构（重建树时，需要确保节点状态一致）
    final oldNodeMap = {for (var node in _allRows) node.id: node};

    _allRows = convertToTreeData(_allRows.map((row) => row.data).toList(), oldNodeMap: oldNodeMap);

    // 更新 _flattenedRows
    _initializeVisibleRows();

    //// 不要滚动到最下面，这会触发 onLoadMore 事件
    // _handleScrollVertical();

    _markRowsChanged();
    notifyListeners();
  }

  ///////////////////////////////////////////////////
  ///测试，改用统一的tableViewportHeight、tableViewportWidth
  ///不需要外部传入

  /// 添加新行
  /// [parentId] 可选，指定父节点id（树结构时用），不传则为一级节点
  void addRow({String? parentId}) {
    _allRows.add(newRow(parentId: parentId, cols: _columns));

    // 重新构建树结构和可见行
    _allRows = convertToTreeData(_allRows.map((row) => row.data).toList());
    _initializeVisibleRows();

    _handleScrollVertical();

    _markRowsChanged();
    notifyListeners();
  }

  /// 删除行
  /// [rowId] 要删除的行ID
  void removeRow(String rowId) {
    var isTree = treeConfig != null;

    if (isTree) {
      // 树结构：删除节点及其所有子节点
      _removeNodeAndChildren(rowId);
    } else {
      // 扁平结构：直接删除记录
      _allRows.removeWhere((row) => row.id == rowId);
    }

    // 重新构建树结构和可见行
    // _allRows = convertToTreeData(_allRows.map((row) => row.data).toList());
    // 重新初始化可见行
    _initializeVisibleRows();

    _markRowsChanged();
    notifyListeners();
  }

  /// 删除节点及其所有子节点（递归删除）
  void _removeNodeAndChildren(String nodeId) {
    // 找到要删除的节点
    var targetNode = _allRows.firstWhere(
      (row) => row.id == nodeId,
      orElse: () => throw Exception('节点不存在: $nodeId'),
    );

    // 收集所有要删除的节点ID（包括子节点、孙节点等）
    Set<String> idsToRemove = <String>{};
    _collectChildrenIds(nodeId, idsToRemove);
    idsToRemove.add(nodeId); // 添加自身

    // 批量删除所有相关节点
    _allRows.removeWhere((row) => idsToRemove.contains(row.id));
  }

  /// 递归收集所有子节点ID
  void _collectChildrenIds(String parentId, Set<String> idsToRemove) {
    // 找到所有直接子节点
    var children = _allRows.where((row) => row.parentId == parentId).toList();

    for (var child in children) {
      idsToRemove.add(child.id);
      // 递归收集子节点的子节点
      _collectChildrenIds(child.id, idsToRemove);
    }
  }

  /// 批量删除多行
  /// [rowIds] 要删除的行ID列表
  void removeRows(List<String> rowIds) {
    if (rowIds.isEmpty) return;

    var isTree = treeConfig != null;

    if (isTree) {
      // 树结构：删除所有节点及其子节点
      Set<String> allIdsToRemove = <String>{};

      for (var rowId in rowIds) {
        _collectChildrenIds(rowId, allIdsToRemove);
        allIdsToRemove.add(rowId);
      }

      _allRows.removeWhere((row) => allIdsToRemove.contains(row.id));
    } else {
      // 扁平结构：直接删除
      _allRows.removeWhere((row) => rowIds.contains(row.id));
    }

    // 重新初始化可见行
    _initializeVisibleRows();

    _markRowsChanged();
    notifyListeners();
  }

  /// 删除指定索引的可见行
  /// [visibleIndex] 可见行列表中的索引
  void removeRowByVisibleIndex(int visibleIndex) {
    if (visibleIndex < 0 || visibleIndex >= _flattenedRows.length) {
      throw Exception('索引超出范围: $visibleIndex');
    }

    var targetRow = _flattenedRows[visibleIndex];
    removeRow(targetRow.id);
  }

  /// 检查节点是否有子节点
  bool hasChildren(String nodeId) {
    return _allRows.any((row) => row.parentId == nodeId);
  }

  /// 获取节点的所有子节点
  List<AppTableRowData> getChildren(String nodeId) {
    return _allRows.where((row) => row.parentId == nodeId).toList();
  }

  /// 获取节点的所有后代节点（包括子节点、孙节点等）
  List<AppTableRowData> getAllDescendants(String nodeId) {
    List<AppTableRowData> descendants = [];
    Set<String> descendantIds = <String>{};

    _collectChildrenIds(nodeId, descendantIds);

    for (var id in descendantIds) {
      var node = _allRows.firstWhere((row) => row.id == id);
      descendants.add(node);
    }

    return descendants;
  }

  void _handleScrollVertical() {
    if (!verticalScrollController.hasClients ||
        !verticalScrollController.position.hasContentDimensions) {
      return;
    }

    //y轴滚动条最大滚动高度（如果为0，表示没有滚动条）
    var max = verticalScrollController.position.maxScrollExtent;
    var curr = verticalScrollController.offset;
    if (max > 0) {
      // //可视区域高度
      // var viewportHeight = tableHeight - max;
      // // 可视区域的空白区域高度
      // var pdHeight = whiteSpaceHeight - (max - curr);

      // print('可是区域：${viewportHeight}，空白区域：${pdHeight}');
      if (max - curr < 50) {
        /// 避免添加行后，把添加行按钮顶出可视区域
        verticalScrollController.jumpTo(verticalScrollController.offset + rowHeight);
      }
    }
  }

  /// 添加新列
  void saveColumn(AppTableColumn column) {
    //

    if (columnStatus == 'add') {
      final String newField = _uuid.v4();
      double columnWidth = 200;
      column.field = newField;
      column.width = columnWidth;
      _columns.add(column);
      // _columnsKeys.add(GlobalKey());

      // 给所有行数据补充新字段
      _assignValueToAllRows(column.type.defaultValue, newField);

      /// 避免添加列后，把添加列按钮顶出可视区域
      var temp = horizontalScrollController.hasClients ? horizontalScrollController.offset : 0;
      if (tbViewportWidth < tableContentWidth + temp) {
        horizontalScrollController.jumpTo(temp + columnWidth);
      }
    } else if (columnStatus == 'edit') {
      var idx = _columns.indexWhere((element) => element.field == column.field);
      if (idx > -1) {
        var old = _columns[idx];

        _columns[idx] = AppTableColumn(
          field: old.field,
          text: column.text,
          type: column.type,
          alignment: column.alignment,
          width: column.width,
          minWidth: column.minWidth,
          cellBuilder: column.cellBuilder,
          headerBuilder: column.headerBuilder,
          sortable: column.sortable,
          resizable: column.resizable,
          frozen: column.frozen,
        );

        // 修改列后，为已存在的行重新赋值
        _assignValueToAllRows(column.type.defaultValue, old.field);
      }
    }

    _markColumnsChanged();
    notifyListeners();
  }

  /// 添加、编辑列后，为已存在的行赋值
  void _assignValueToAllRows(dynamic value, String field) {
    for (var row in _allRows) {
      if (value is List) {
        Map<String, dynamic> cols = <String, dynamic>{};
        cols.addAll(row.data); // 或其他方式复制数据
        cols[field] = List.from(value);
        row.data = cols;
      } else {
        row.data[field] = value.toString();
      }
    }
  }

  void removeColumn(String field) {
    // 删除行里面对应列的数据
    for (var row in _allRows) {
      row.data.remove(field);
    }
    //删除列
    _columns.removeWhere((element) => element.field == field);

    _markColumnsChanged();
    notifyListeners();
  }

  // 获取节点的根节点ID（level=0的节点的ID）
  String? _getRootNodeId(AppTableRowData row) {
    if (row.level == 0) return row.id;

    String? currentParentId = row.parentId;
    AppTableRowData? currentParent;

    // 向上查找直到找到 level=0 的节点
    while (true) {
      currentParent = _allRows.firstWhere((r) => r.id == currentParentId);
      if (currentParent.level == 0) {
        return currentParent.id;
      }
      currentParentId = currentParent.parentId;
    }
  }

  // 判断指定索引的行是否是其最顶层组的最后一个可见元素
  bool isLastVisibleInGroup(int index) {
    if (index >= _flattenedRows.length - 1) return true;

    AppTableRowData currentRow = _flattenedRows[index];
    AppTableRowData nextRow = _flattenedRows[index + 1];

    // 如果当前行是一级节点，且下一行也是一级节点，则当前行是其组的最后一个
    if (currentRow.level == 0 && nextRow.level == 0) {
      return true;
    }

    // 获取当前行和下一行的根节点ID
    String? currentRootId = currentRow.level == 0 ? currentRow.id : _getRootNodeId(currentRow);
    String? nextRootId = nextRow.level == 0 ? nextRow.id : _getRootNodeId(nextRow);

    // 如果根节点不同，说明当前行是其组的最后一个可见元素
    return currentRootId != nextRootId;
  }

  // 修改：初始化可见行的方法
  // 修改：_initializeVisibleRows 方法也使用相同的逻辑
  // 在 _initializeVisibleRows 方法末尾添加
  void _initializeVisibleRows() {
    if (defaultExpandAll) {
      // 从根节点开始，按照展开状态构建可见行列表
      _flattenedRows = [];
      List<AppTableRowData> rootNodes = _allRows.where((row) => row.parentId == null).toList();

      for (var rootNode in rootNodes) {
        _flattenedRows.add(rootNode);
        if (rootNode.isExpanded) {
          _flattenedRows.addAll(_getVisibleChildren(rootNode));
        }
      }
    } else {
      // 只显示根节点
      _flattenedRows = _allRows.where((row) => row.parentId == null).toList();
    }

    // 更新表格选中状态
    _updateTableCheckStatus();
  }

  // 将原始数据转换为树形结构
  List<AppTableRowData> convertToTreeData(
    List<Map<String, dynamic>> rawData, {
    Map<String, AppTableRowData>? oldNodeMap,
  }) {
    var isTree = treeConfig != null;

    // 构建旧节点Map（id -> AppTableRowData）
    oldNodeMap ??= {for (var node in _allRows) node.id: node};

    // 扁平结构：每条数据都是一级节点
    if (!isTree) {
      return rawData.map((row) {
        var id = row[uniqueId].toString();
        var oldNode = oldNodeMap![id];
        return AppTableRowData(
          data: row,
          level: 0,
          hasChildren: false,
          parentId: null,
          id: id,
          isExpanded: oldNode?.isExpanded ?? defaultExpandAll,
          children: null,
          levelOneIndex: null,
          childrenCount: 0,
          descendantsCount: 0,
          isLeaf: true,
          isChecked: oldNode?.isChecked ?? false,
        );
      }).toList();
    }

    /// 树结构
    // 先创建一个 Map 用于快速查找节点
    Map<String?, AppTableRowData> nodeMap = {};

    // 跟踪一级节点的索引
    int levelOneCounter = 0;
    // 第一遍循环：创建所有节点
    for (var row in rawData) {
      // 确定是否是一级节点（根节点）
      bool isLevelOne = row[treeConfig!.parentIdField] == treeConfig!.emptyParentValue;
      var id = row[treeConfig!.idField].toString();
      var oldNode = oldNodeMap![id];
      var node = AppTableRowData(
        data: row,
        level: 0, // 初始设置为0，后面会更新
        hasChildren: false,
        parentId: row[treeConfig!.parentIdField] ?? treeConfig!.emptyParentValue,
        id: id,
        isExpanded: oldNode?.isExpanded ?? defaultExpandAll,
        levelOneIndex: isLevelOne ? levelOneCounter++ : null,
        childrenCount: 0,
        descendantsCount: 0,
        isLeaf: true,
        isChecked: oldNode?.isChecked ?? false,
      );
      nodeMap[node.id] = node;
    }

    // // 第二遍循环：建立父子关系并更新 hasChildren 和 level
    // void updateNodeLevel(AppTableRowData node, int level) {
    //   node.level = level;
    //   // node.childrenCount = node.children?.length ?? 0;
    //   if (node.children != null) {
    //     for (var child in node.children!) {
    //       updateNodeLevel(child, level + 1);
    //     }
    //   }
    // }

    // 递归设置 level、childrenCount、descendantsCount
    int updateNodeStats(AppTableRowData node, int level) {
      node.level = level;
      node.childrenCount = node.children?.length ?? 0;
      int totalDescendants = 0;
      bool hasVisibleChildren = false;

      if (node.children != null) {
        for (var child in node.children!) {
          totalDescendants += 1 + updateNodeStats(child, level + 1);
          if (child.isExpanded) {
            hasVisibleChildren = true;
          }
        }
      }
      node.descendantsCount = totalDescendants;

      // 只有当有可见（展开）的子节点时，才不是叶子节点
      node.isLeaf = !hasVisibleChildren;

      return totalDescendants;
    }

    // 建立父子关系
    for (var node in nodeMap.values) {
      if (node.parentId != null) {
        var parentNode = nodeMap[node.parentId];
        if (parentNode != null) {
          parentNode.children ??= [];
          parentNode.children!.add(node);
          parentNode.hasChildren = true;
        }
      }
    }

    // 从根节点开始递归设置 level
    for (var node in nodeMap.values) {
      if (node.parentId == null) {
        updateNodeStats(node, 0);
      }
    }

    var result = rawData.map((row) => nodeMap[row[treeConfig!.idField].toString()]!).toList();

    return result;
  }

  /// 只返回应该显示的子节点；考虑每个节点的展开状态；递归处理子节点的可见性
  List<AppTableRowData> _getVisibleChildren(AppTableRowData node) {
    List<AppTableRowData> result = [];

    if (node.children != null && node.isExpanded) {
      for (var child in node.children!) {
        // 添加直接子节点
        result.add(child);

        // 如果子节点是展开状态，递归添加其可见的子节点
        if (child.isExpanded) {
          result.addAll(_getVisibleChildren(child));
        }
      }
    }

    return result;
  }

  // 获取指定索引的行数据
  AppTableRowData getRowData(int index) {
    if (index < 0 || index >= _flattenedRows.length) {
      throw RangeError('Index out of range: $index');
    }
    return _flattenedRows[index];
  }

  // // 获取下一行
  // AppTableRowData? getNextRowData(int currentIndex) {
  //   int nextRowIndex = currentIndex + 1;
  //   return nextRowIndex < rows.length - 1 ? getRowData(nextRowIndex) : null;
  // }

  _setAutoExpandId(String? rowId) {
    _autoExpandId = rowId;
  }

  // 展开/收起节点
  void toggleNode(String? nodeId) {
    var nodeIndex = _flattenedRows.indexWhere((row) => row.id == nodeId);
    if (nodeIndex == -1) return;

    var node = _flattenedRows[nodeIndex];
    node.isExpanded = !node.isExpanded;

    if (node.isExpanded) {
      // 只添加应该显示的子节点
      List<AppTableRowData> visibleChildren = _getVisibleChildren(node);
      _flattenedRows.insertAll(nodeIndex + 1, visibleChildren);
    } else {
      // 收起：移除所有子节点
      var level = node.level;
      var i = nodeIndex + 1;
      while (i < _flattenedRows.length && _flattenedRows[i].level > level) {
        _flattenedRows.removeAt(i);
      }
    }

    _markRowsChanged();
    notifyListeners();
  }

  /// 根据列索引获取列对象
  AppTableColumn getColumnByIndex(int index) {
    if (index < 0 || index >= _columns.length) {
      throw RangeError('Index out of range: $index');
    }
    return _columns[index];
  }

  /// 根据列索引集合获取列宽度总和
  double getWidthByColumns(List<int> columnIndexes) {
    double totalWidth = 0;
    for (int index in columnIndexes) {
      if (index < 0 || index >= _columns.length) {
        throw RangeError('Index out of range: $index');
      }
      totalWidth += _columns[index].width;
    }
    return totalWidth;
  }

  /// 设置选中的列
  void setSelectedColumn(List<int>? idxs) {
    selectedColumnIndexs = idxs ?? [];
    // if (idx == null || idx < 0 || idx >= _columns.length) {
    //   selectedColumnIndex = null;
    //   // selectedColumnWidth = null;
    // } else {
    //   selectedColumnIndex = idx;
    //   // selectedColumnWidth = _columns[idx].width;
    // }
    notifyListeners();
  }

  /// 表示表格的列发生了变化
  void _markColumnsChanged() {
    _columnsChanged++;
  }

  void _markRowsChanged() {
    _rowsChanged++;
  }

  // // 获取指定容器的全局坐标
  // Offset? _getContainerGlobalPosition(BuildContext context, int index) {
  //   final RenderBox? box =
  //       _columnsKeys[index].currentContext?.findRenderObject() as RenderBox?;
  //   if (box != null) {
  //     return box.localToGlobal(Offset.zero);
  //   }
  //   return null;
  // }

  /// 拖拽列改变列宽相关逻辑—————————————————————————————————————————————————————————————————————————————————————————
  /// 列宽调整相关状态

  // 列宽调整拖拽标记
  final Color _dragBlockColor = Colors.red;
  final double _dragBlockWidth = 14;
  // 列宽调整提示线
  final Color _dragSplitlineColor = Colors.red;
  final double _dragSplitlineWidth = 1;
  int? _resizingColumnIndex; //拖拽的列索引
  double? _startX; // 点击拖拽开始时的x坐标（相对于表格左上角）
  double? _offsetX; // 拖拽的x轴距离
  double? _dxOfDragBlock; // 拖拽标记的x轴偏移量（拖拽块可能比较宽，点击时不一定，为了准确计算提示线位置，所以需要记录该数值）

  Color get dragBlockColor => _dragBlockColor;
  double get dragBlockWidth => _dragBlockWidth;
  Color get dragSplitlineColor => _dragSplitlineColor;
  double get dragSplitlineWidth => _dragSplitlineWidth;
  int? get resizingColumnIndex => _resizingColumnIndex;
  double? get startX => _startX;
  double? get offsetX => _offsetX;

  void resizeDragStart(int? resColumnIndex, Offset? tablePosition, DragStartDetails details) {
    _resizingColumnIndex = resColumnIndex;
    _dxOfDragBlock = details.localPosition.dx;

    _startX =
        details.globalPosition.dx - tablePosition!.dx + (_dragBlockWidth / 2 - _dxOfDragBlock!) - 1;
    _offsetX = 0;

    notifyListeners();
  }

  void resizeDragUpdate(Offset? tablePosition, DragUpdateDetails details, AppTableColumn column) {
    var temp = _startX! - (details.globalPosition.dx - tablePosition!.dx);
    if (column.width - column.minWidth > temp) {
      _offsetX = temp;
    }
    notifyListeners();
  }

  void resizeDragEnd(AppTableColumn column, int columnIndex) {
    if (_resizingColumnIndex == columnIndex) {
      var newWidth = column.width - _offsetX!;
      _columns[columnIndex] = AppTableColumn(
        field: column.field,
        text: column.text,
        type: column.type,
        width: newWidth,
        minWidth: column.minWidth,
        alignment: column.alignment,
        cellBuilder: column.cellBuilder,
        headerBuilder: column.headerBuilder,
        sortable: column.sortable,
        resizable: column.resizable,
        frozen: column.frozen,
      );

      _resizingColumnIndex = null;
      _startX = null;
      _offsetX = null;

      _markColumnsChanged();
    }

    notifyListeners();
  }

  /// 拖拽冻结列相关逻辑—————————————————————————————————————————————————————————————————————————————————————————
  /// 冻结列提示线容器宽度（因为需要阴影（boxShadow），如果设置过小，滚动y轴时，可能会导致阴影消失）
  double get frozenLineWidth => 12;

  /// 阴影宽度，设置过大（相对frozenLineWidth），可能导致滚动y轴时，阴影消失
  double get boxShadowWidth => 3;

  /// 提示线默认left
  double? get frozenLineXDefault => tableWidthOfFrozenArea - boxShadowWidth - 6;

  /// 点击冻结列提示线的相对位置
  double? _dxOfDragFrozenColumnBlock = null;

  /// 鼠标hover提示线的位置
  Offset? _frozenLineHoverStart = null;

  double? _frozenLineX = null;

  // /// 冻结列提示线的高度（虚线）
  // double? _frozenLineHeight = null;

  /// 主要用来界定鼠标 exit 冻结线是直接移开，还是拖拽移开；
  /// 鼠标点击（onHorizontalDragStart）设置为true；鼠标松开（onHorizontalDragEnd）设置为false；
  bool _isDragStarted = false;

  Offset? get frozenLineHoverStart => _frozenLineHoverStart;
  double? get frozenLineX => _frozenLineX;
  // double? get frozenLineHeight => _frozenLineHeight;
  bool get isDragStarted => _isDragStarted;

  // /// 设置冻结列线位置
  void setFrozenLinex() {
    _frozenLineX = tableWidthOfFrozenArea;
  }

  /// 设置冻结列线位置（鼠标hover时）
  /// [globalOffset] 鼠标全局位置
  /// [localOffset] 鼠标相对于冻结线的位置
  void setFrozenLineHoverStart(Offset? globalOffset, Offset? localOffset) {
    // 鼠标 hover 时
    if (globalOffset != null && localOffset != null) {
      _dxOfDragFrozenColumnBlock = localOffset.dx;
      _frozenLineHoverStart = Offset(globalOffset!.dx - localOffset!.dx, globalOffset.dy);
    } else {
      // 鼠标 exit 时
      _frozenLineHoverStart = null;
    }

    notifyListeners();
  }

  /// 表格可是内容高度（表头header部分到内容的高度）
  double get viewportHeight {
    if (!verticalScrollController.hasClients) return 0;
    if (!verticalScrollController.position.hasContentDimensions) return 0;

    //计算提示线的高度
    //y轴滚动条最大滚动高度（如果为0，表示没有滚动条）
    var max = verticalScrollController.position.maxScrollExtent;

    var curr = verticalScrollController.offset;
    if (max == 0) {
      return tableBodyHeight;
    } else {
      //可视区域高度
      var viewportHeight = tableHeight - max;
      // 可视区域的空白区域高度
      var pdHeight = whiteSpaceHeight - (max - curr);
      // 偶尔计算会超出一点，所以 - 2
      return viewportHeight - pdHeight + headerHeight - 2;
    }
  }

  void frozenLineDragStart(
    Offset? tablePosition,
    DragStartDetails details,
    // int dragColIdx,
    BuildContext context,
  ) {
    _isDragStarted = true;

    if (horizontalScrollController.hasClients) {
      // 点击拖拽冻结列之前，跳转到最左侧（0位置）
      horizontalScrollController.jumpTo(0);
    }

    notifyListeners();
  }

  void frozenDragUpdate(
    BuildContext context,
    Offset? tablePosition,
    Size tableSize,
    DragUpdateDetails details,
    // int dragColIdx,
  ) {
    if (_frozenLineHoverStart != null) {
      _frozenLineHoverStart = Offset(
        details.globalPosition.dx - (_dxOfDragFrozenColumnBlock ?? 0),
        _frozenLineHoverStart!.dy,
      );

      _handleMarkInsertIdx(context, tablePosition, details.globalPosition, 2);

      notifyListeners();
    }
  }

  frozenLineDragEnd() {
    if (_frozenLineHoverStart != null) {
      _isDragStarted = false;
      _frozenLineHoverStart = null;
      _dxOfDragFrozenColumnBlock = null;

      //表示匹配到冻结列位置，并且不是第一列
      var insIdx = _insertIdx;

      if (insIdx > 0) {
        insIdx -= 1;

        for (var i = 0; i < _columns.length; i++) {
          if (i <= insIdx) {
            _columns[i].frozen = AppTableColumnFrozen.start;
          } else {
            _columns[i].frozen = AppTableColumnFrozen.none;
          }
        }

        _markColumnsChanged();
      }

      _insertIdx = -1;
      _insertX = -1;

      notifyListeners();
    }
  }

  /// 拖拽列改变排序相关逻辑—————————————————————————————————————————————————————————————————————————————————————————
  //选中列头单元格背景颜色
  final Color? _selectedColumnColor = const Color.fromARGB(255, 226, 235, 242);
  final Color? _rowHoverBgColor = const Color.fromARGB(255, 184, 169, 169);

  Offset? _startGlobalPosition; // 拖拽列开始时的全局位置（相对于屏幕左上角）
  Offset? _startGlobalPositionOrigina; //原始的鼠标开始位置，只能初始化或者清空

  /// 测试代码
  Offset? _endGlobalPosition; // 拖拽列结束时的全局位置（相对于屏幕左上角）
  MousePositionOfTable? _mousePositionOfTable;
  // 拖拽列相关变量
  double? _dxOfDragColumnBlock; // 点击单元哥的位置
  //拖拽列后，需要插入的位置
  int _insertIdx = -1;
  //拖拽列后，需要插入提示线的坐标
  double _insertX = -1;
  //提示线宽度
  double _insertSplitlineWidth = 2;
  //提示线颜色
  Color _insertSplitlineColor = Color(int.parse("0xFF009e8b"));

  //拖拽线匹配宽度（举例目标位置多远能够匹配到）
  double _insertSplitlineMatchWidth = 100;

  bool _isPanning = false; // 是否正拖拽框选列头
  bool _isDragColumn = false; //是否正拖拽选中列列头（排序）
  bool _autoScrollFlag = false;

  Offset? get startGlobalPosition => _startGlobalPosition;
  Offset? get endGlobalPosition => _endGlobalPosition;

  Color? get selectedColumnColor => _selectedColumnColor;
  Color? get rowHoverBgColor => _rowHoverBgColor;
  double? get dxOfDragColumnBlock => _dxOfDragColumnBlock;
  int get insertIdx => _insertIdx;
  double get insertX => _insertX;
  double get insertSplitlineWidth => _insertSplitlineWidth;
  Color get insertSplitlineColor => _insertSplitlineColor;
  bool get isPanning => _isPanning;
  bool get isDragColumn => _isDragColumn;

  /// 计算当前拖拽列的左侧、右侧选中列的宽度总和，
  ///
  /// 背景：
  /// 将拖拽列的LocalPosition转换为选中（多列）的位置
  /// 没转换之前，点击列头单元格通过 details.localPosition.dx 获取到的位置是相对当前单元格的位置；
  /// 但是因为列头需要支持多选，所以需要将点击列头单元格的位置映射为所有选中列的位置；
  /// 假设：当你点击第3列100的位置时，获取到的是100；但是此时第2、3列都选中了，第二列宽度为200，那么隐射出来的位置为 300；
  ///
  double _calculatesFrontOrBehindSelectedColumnsTotalWidthByDragColIdx(
    int dragColIdx,
    bool isLeft,
  ) {
    double result = 0;
    for (var i = 0; i < selectedColumnIndexs.length; i++) {
      if (isLeft) {
        if (dragColIdx > selectedColumnIndexs[i]) {
          result += _columns[selectedColumnIndexs[i]].width;
        }
      } else {
        if (dragColIdx < selectedColumnIndexs[i]) {
          result += _columns[selectedColumnIndexs[i]].width;
        }
      }
    }
    return result;
  }

  // 添加定时器相关变量
  Timer? _autoScrollTimer;
  static const double _autoScrollSpeed = 5.0; // 每次滚动的像素数
  static const int _autoScrollInterval = 16; // 约60fps的刷新率

  // 添加定时器相关变量
  Timer? _autoScrollTimerVertical;

  /// 自动滚动方法（自动滚动实现逻辑，记录基于全局的开始位置，和截止位置，滚动时，更新开始位置，终止位置不变）
  void _startAutoScroll(BuildContext context, Offset? tablePosition, {isRightScroll = true}) {
    // 如果已经在滚动，先停止
    _stopAutoScroll();

    // 创建定时器，每16ms执行一次滚动
    _autoScrollTimer = Timer.periodic(Duration(milliseconds: _autoScrollInterval), (timer) {
      if (!horizontalScrollController.hasClients) {
        _stopAutoScroll();
        return;
      }

      //处理滚动
      handleScroll(
        context,
        tablePosition: tablePosition,
        isRightScroll: isRightScroll,
        rollingDistance: _autoScrollSpeed,
        isAutoScroll: true,
      );
    });
  }

  /// 滚动处理方法
  /// [tablePosition] 表格位置信息
  /// [isRightScroll] true，向右滚动；false：向左滚动；
  /// [rollingDistance] 滚动距离
  /// [isAutoScroll] 为true，针对手动框选、或拖拽选中列时，鼠标跑到表格左边或者右边外部区域；false 针对通过shift+滚动滚动的情况，只是为了快速找到需要款选的列，但是款选还是需要通过鼠标移动来实现）
  ///
  void handleScroll(
    BuildContext context, {
    Offset? tablePosition,
    bool isRightScroll = true,
    double rollingDistance = 5,
    bool isAutoScroll = false,
  }) {
    if (_startGlobalPosition == null ||
        !horizontalScrollController.hasClients ||
        !horizontalScrollController.position.hasContentDimensions) {
      // 如果没有起始位置，说明不是框选操作，直接返回
      return;
    }

    // 获取当前滚动位置
    final currentScroll = horizontalScrollController.offset;
    // 获取最大滚动范围
    final maxScroll = horizontalScrollController.position.maxScrollExtent;

    // 不能向右滚动
    var notAllowedRightScroll = isRightScroll && currentScroll >= maxScroll;
    // 不能向左滚动
    var notAllowedLeftScroll = !isRightScroll && currentScroll <= 0;
    // 如果已经滚动到最右侧，停止滚动
    if (notAllowedRightScroll || notAllowedLeftScroll) {
      _stopAutoScroll();
      return;
    }

    /// 假设当前滚动位置为100，最大滚动范围为120，但是每次滚动30；
    /// 所以希望滚动到的位置为130，但是实际最大只能滚动到120；两者差值为10（少滚动了10）；
    /// 所以实际滚动的速度为20（30 - 10）；
    // 计算新的滚动位置（滚动后的位置）
    var newOffset = currentScroll + (isRightScroll ? rollingDistance : -rollingDistance);

    // 实际能否移动到的位置（例如：可能希望向右滚动50，但是实际上最大只能滚动20）
    var actualNewOffset = newOffset.clamp(0.0, maxScroll);

    if (isAutoScroll) {
      // 使用 jumpTo 进行滚动
      horizontalScrollController.jumpTo(actualNewOffset);
    }

    // 计算实际滚动的差值（例如：希望滚动50，但是实际只能滚动20）
    var difference = (actualNewOffset - newOffset).abs();

    // 实际滚动了多少
    var actualAutoScrollSpeed = rollingDistance;

    if (difference > 0) {
      actualAutoScrollSpeed = rollingDistance - difference;
    }

    // 更新起始位置
    _startGlobalPosition = Offset(
      isRightScroll
          ? _startGlobalPosition!.dx - actualAutoScrollSpeed
          : _startGlobalPosition!.dx + actualAutoScrollSpeed,
      _startGlobalPosition!.dy,
    );

    if (isAutoScroll) {
      if (_isPanning) {
        // 执行框选操作——只有在框选时，自动滚动才会执行选中操作
        _handleSelectedColumn(context, tablePosition);
      } else if (_isDragColumn) {
        // 清空列位置提示线——拖拽时，自动滚动不执行框选操作，一定要手动移动匹配到插入的位置。
        _handleMarkInsertIdx(context, tablePosition, _endGlobalPosition, 1);
      }
    }
  }

  // 停止自动滚动
  void _stopAutoScroll() {
    _autoScrollTimer?.cancel();
    _autoScrollTimer = null;
  }

  void _stopAutoScrollVertical() {
    _autoScrollTimerVertical?.cancel();
    _autoScrollTimerVertical = null;
  }

  /// 获取所有列的的起止位置
  /// 假设：所有列的列宽分别为：[100, 160, 120, 80, 100]
  /// 那么返回集合为：
  /// [
  ///   {left: 0, right: 100},
  ///   {left: 100, right: 260},
  ///   {left: 260, right: 380},
  ///   {left: 380, right: 460},
  ///   {left: 460, right: 540}
  /// ]
  ///
  /// 注意：
  /// 1、第一个对象列的left为0是相对的，根据具体第一列的位置来决定的；
  ///
  // List<ColumnPosition> _globalStarStartAndEndPositionsOfColumns(
  //   BuildContext context,
  // ) {
  //   List<ColumnPosition> result = [];
  //   for (var i = 0; i < _columns.length; i++) {
  //     var curColWidth = _columns[i].width;

  //     var box = _getContainerGlobalPosition(context, i);
  //     ColumnPosition position = ColumnPosition(
  //       left: box!.dx,
  //       right: box!.dx + curColWidth,
  //     );
  //     result.add(position);
  //   }

  //   return result;
  // }

  /// 返回所有列分割点位置
  /// 假设：所有列的列宽分别为：[100, 160, 120, 80, 100]
  /// 那么返回集合为：
  /// [0, 100, 260, 380, 460, 540]
  ///
  /// 注意：
  /// 1、第一个元素0是相对的，根据具体第一列的位置来决定的；
  ///
  List<double> globalPositionsOfColumns(BuildContext context, Offset? tablePosition) {
    List<double> result = [];

    double flag = indexColumnWidth + tablePosition!.dx;
    for (int i = 0; i < _columns.length; i++) {
      var curr = _columns[i];
      var curColWidth = curr.width;
      if (i == 0) {
        result.add(flag);
      }

      flag += curColWidth;

      double scrollOffset = 0;
      if (horizontalScrollController.hasClients) {
        scrollOffset = horizontalScrollController.offset;
      }
      // //如果是非冻结的列，需要考虑滚动条滚动的距离
      // if (curr.frozen == AppTableColumnFrozen.none) {
      //   scrollOffset = horizontalScrollController.offset;
      // }
      result.add(flag - scrollOffset);
    }

    return result;
  }

  /// 获取所有列的的起止位置（Global）
  /// 假设：所有列的列宽分别为：[100, 160, 120, 80, 100]
  /// 那么返回集合为：
  /// [
  ///   {left: 0, right: 100},
  ///   {left: 100, right: 260},
  ///   {left: 260, right: 380},
  ///   {left: 380, right: 460},
  ///   {left: 460, right: 540}
  /// ]
  ///
  /// 注意：
  /// 1、第一个对象列的left为0是相对的，根据具体第一列的位置来决定的
  /// （需要加上表格偏移量+索引列的宽度）；
  /// 2、需要考虑x轴滚动条；不管是冻结列还是非冻结列（冻结列虽然固定在左侧，但是为了方便计算，可以想象成和非冻结列一样布局）；
  ///
  /// 所有列的位置（左侧和右侧）——包含索引列（索引列在最左侧写死）
  /// [tablePosition]表格的偏移量，例如表格左侧空白40，需要参与到计算；
  /// 注意：因为（第一）列的左侧考虑了表格偏移量，以及索引列宽度，得到的数值其实是列前面的宽度，
  /// 所以真正的列边界应该是（left < 列宽 <= right）
  List<ColumnPosition> _getPositionsOfColumns(Offset? tablePosition) {
    List<ColumnPosition> result = [];
    double flag = indexColumnWidth + tablePosition!.dx;
    for (int i = 0; i < _columns.length; i++) {
      var curr = _columns[i];
      var curColWidth = curr.width;
      var flagRight = flag + curColWidth;

      double scrollOffset = 0;
      if (horizontalScrollController.hasClients) {
        scrollOffset = horizontalScrollController.offset;
      }

      // //如果是非冻结的列，需要考虑滚动条滚动的距离
      // if (curr.frozen == AppTableColumnFrozen.none) {
      //   scrollOffset = horizontalScrollController.offset;
      // }
      var col = ColumnPosition(left: flag - scrollOffset, right: flagRight - scrollOffset);
      flag = flagRight;
      result.add(col);
    }
    return result;
  }

  //标记选中列
  void _handleSelectedColumn(BuildContext context, Offset? tablePosition) {
    //鼠标起、止位置（小的当作起点，大的当作终点）
    double globalStart =
        _startGlobalPosition!.dx < _endGlobalPosition!.dx
            ? _startGlobalPosition!.dx
            : _endGlobalPosition!.dx;
    double globalEnd =
        _startGlobalPosition!.dx > _endGlobalPosition!.dx
            ? _startGlobalPosition!.dx
            : _endGlobalPosition!.dx;

    //所有的列的起止位置
    var colsPositions = _getPositionsOfColumns(tablePosition);

    //纠正起止位置（如果小于第一列的left，则置为第一列的left）
    globalStart = globalStart < colsPositions[0].left ? colsPositions[0].left : globalStart + 1;

    //纠正起止位置（如果大于最后一列的right，则置为最后一列的right）
    globalEnd = globalEnd > colsPositions.last.right ? colsPositions.last.right : globalEnd - 1;

    //起始位置所对应列的索引
    var startIdx = colsPositions.indexWhere((c) => c.left <= globalStart && c.right >= globalStart);

    //终点位置对应列的索引
    var endIdx = colsPositions.indexWhere((c) => c.left <= globalEnd && c.right >= globalEnd);

    //生成连续的列索引
    List<int> indices = List.generate(endIdx - startIdx + 1, (i) => startIdx + i);

    // 通过索引选中列
    setSelectedColumn(indices);

    notifyListeners();
  }

  /// 展示错误信息（替换成 tooltip 组件）
  void showMessage(String msg) {
    AppTableGeneralHelper.showMessage(msg);
  }

  /// 标记插入列索引，标记插入列提示线位置
  /// [context]
  /// [tablePosition] 表格的偏移量，例如表格左侧空白40，需要参与到计算；
  /// [position] 鼠标位置
  /// [busType] 业务逻辑（匹配规则）1：拖拽列插入匹配位置；2：拖拽冻结列匹配位置
  void _handleMarkInsertIdx(
    BuildContext context,
    Offset? tablePosition,
    Offset? position,
    int busType,
  ) {
    var allPosition = globalPositionsOfColumns(context, tablePosition);

    var matchIdx = allPosition.indexWhere(
      (element) => (position!.dx - element).abs() < _insertSplitlineMatchWidth,
    );

    // matchIdx > -1 必须匹配到索引，且排除选中的列对应的索引
    //
    var isMatch = false;

    if (busType == 1) {
      isMatch =
          (matchIdx < selectedColumnIndexs[0] ||
              matchIdx > selectedColumnIndexs[selectedColumnIndexs.length - 1] + 1);
    } else if (busType == 2) {
      //至少需要保留一列冻结列，所以不能匹配到第一列
      isMatch = matchIdx > 0 && allPosition[matchIdx] <= frozenAreaMaxWidth;
    }

    if (matchIdx > -1 && isMatch) {
      _insertIdx = matchIdx;
      _insertX = allPosition[matchIdx] - tablePosition!.dx - 1;

      if (busType == 2) {
        if (allPosition[matchIdx] > frozenAreaMaxWidth) {
          showMessage('冻结列不能太宽');
        }
      }

      notifyListeners();
    }
  }

  ///鼠标是否在表格的某个位置
  /// [target] 参考位置
  bool _mouseIsOutside(
    Offset? tablePosition,
    Size tableSize,
    Offset mousePosition,
    MousePositionOfTable target,
  ) {
    switch (target) {
      case MousePositionOfTable.rightOutside:
        var rightSideX = tablePosition!.dx + tableSize.width;
        return mousePosition.dx > rightSideX;
      case MousePositionOfTable.leftOutside:
        var leftSideX = tablePosition!.dx;
        leftSideX += tableWidthOfFrozenArea;
        return mousePosition.dx < leftSideX;
      case MousePositionOfTable.topOutside:
        var topSideX = tablePosition!.dy;
        return mousePosition.dy < topSideX;
      case MousePositionOfTable.bottomOutside:
        var bottomSideX = tablePosition!.dy + tableSize.height;
        return mousePosition.dy > bottomSideX;
      default:
        return false;
    }
  }

  // /// 鼠标是否在表格右侧外边
  // bool _mouseIsRightOutside(
  //   Offset? tablePosition,
  //   Size tableSize,
  //   Offset mousePosition,
  // ) {
  //   var rightSideX = tablePosition!.dx + tableSize.width;
  //   return mousePosition.dx > rightSideX;
  // }

  // /// 鼠标是否在表格左侧外边
  // bool _mouseIsLeftOutside(
  //   Offset? tablePosition,
  //   Size tableSize,
  //   Offset mousePosition,
  // ) {
  //   var leftSideX = tablePosition!.dx;
  //   return mousePosition.dx < leftSideX;
  // }

  void _startAutoScrollVertical(
    BuildContext context,
    Offset? tablePosition,
    Size tableSize, {
    isTopScroll = true,
  }) {
    // 如果已经在滚动，先停止
    _stopAutoScrollVertical();

    // 创建定时器，每16ms执行一次滚动
    _autoScrollTimerVertical = Timer.periodic(Duration(milliseconds: _autoScrollInterval), (timer) {
      if (!verticalScrollController.hasClients) {
        _stopAutoScrollVertical();
        return;
      }

      //处理滚动
      handleScrollVertical(
        context,
        tablePosition: tablePosition,
        tableSize: tableSize,
        isTopScroll: isTopScroll,
        rollingDistance: _autoScrollSpeed,
        isAutoScroll: true,
      );
    });
  }

  void handleScrollVertical(
    BuildContext context, {
    Offset? tablePosition,
    Size? tableSize,
    bool isTopScroll = true,
    double rollingDistance = 5,
    bool isAutoScroll = false,
  }) {
    if (_startRowGlobalPosition == null ||
        !verticalScrollController.hasClients ||
        !verticalScrollController.position.hasContentDimensions) {
      // 如果没有起始位置，说明不是框选操作，直接返回
      return;
    }

    // 获取当前滚动位置
    final currentScroll = verticalScrollController.offset;
    // 获取最大滚动范围
    final maxScroll = verticalScrollController.position.maxScrollExtent;

    // 不能向上滚动
    var notAllowedTopScroll = isTopScroll && currentScroll <= 0;
    // 不能向下滚动
    var notAllowedBottomScroll = !isTopScroll && currentScroll >= maxScroll;

    // 如果已经滚动到最右侧，停止滚动
    if (notAllowedTopScroll || notAllowedBottomScroll) {
      _stopAutoScrollVertical();
      return;
    }

    /// 假设当前滚动位置为100，最大滚动范围为120，但是每次滚动30；
    /// 所以希望滚动到的位置为130，但是实际最大只能滚动到120；两者差值为10（少滚动了10）；
    /// 所以实际滚动的速度为20（30 - 10）；
    // 计算新的滚动位置（滚动后的位置）
    var newOffset = currentScroll + (isTopScroll ? -rollingDistance : rollingDistance);

    // 实际能否移动到的位置（例如：可能希望向右滚动50，但是实际上最大只能滚动20）
    var actualNewOffset = newOffset.clamp(0.0, maxScroll);

    if (isAutoScroll) {
      // 使用 jumpTo 进行滚动
      verticalScrollController.jumpTo(actualNewOffset);
    }

    // 计算实际滚动的差值（例如：希望滚动50，但是实际只能滚动20）
    var difference = (actualNewOffset - newOffset).abs();

    // 实际滚动了多少
    var actualAutoScrollSpeed = rollingDistance;
    if (difference > 0) {
      actualAutoScrollSpeed = rollingDistance - difference;
    }

    // 更新起始位置
    _startRowGlobalPosition = Offset(
      isTopScroll
          ? _startRowGlobalPosition!.dy - actualAutoScrollSpeed
          : _startRowGlobalPosition!.dy + actualAutoScrollSpeed,
      _startRowGlobalPosition!.dx,
    );

    if (isAutoScroll) {
      if (_isPanningRow) {
        // // 执行框选操作——只有在框选时，自动滚动才会执行选中操作
        // _handleSelectedColumn(context);
      } else if (_isDragRow) {
        // // 清空列位置提示线——拖拽时，自动滚动不执行框选操作，一定要手动移动匹配到插入的位置。
        // _handleMarkInsertIdx(context, tablePosition);
        // print('自动标线');

        _handleMarkRow(context, tablePosition, tableSize);
      }
    }
  }

  // 列能否被拖拽
  bool _canDragColumn() {
    var result = true;
    //所有冻结列的索引
    var idxOfFrozenColumn =
        _columns
            .asMap()
            .keys
            .where((i) => _columns[i].frozen == AppTableColumnFrozen.start)
            .toList();

    // 如果选中的列里面包含了所有的冻结列，不能拖拽，因为至少需要保留一列冻结列
    if (idxOfFrozenColumn.every((element) => selectedColumnIndexs.contains(element))) {
      result = false;
    }
    return result;
  }

  /// 判断 offset 处于表格的什么区域
  AreaOfMouse _getArea(Offset tablePosition, Offset? offset) {
    if (offset!.dx - tablePosition.dx > tableWidthOfFrozenArea) {
      //鼠标位置是否处于非冻结区域
      return AreaOfMouse.notFrozen;
    } else if (offset.dx - tablePosition.dx < tableWidthOfFrozenArea) {
      //鼠标位置是否处于冻结区域
      return AreaOfMouse.frozen;
    }
    return AreaOfMouse.none;
  }

  void columnDragStart(
    Offset? tablePosition,
    DragStartDetails details,
    int dragColIdx,
    BuildContext context,
  ) {
    //记录鼠标开始拖拽的位置
    _startGlobalPosition = details.globalPosition;
    _startGlobalPositionOrigina = details.globalPosition;

    //如果点击的列为冻结列（需要考虑滚动条）
    if (_getArea(tablePosition!, _startGlobalPosition) == AreaOfMouse.frozen) {
      var temp = horizontalScrollController.hasClients ? horizontalScrollController.offset : 0;
      _startGlobalPosition = Offset(_startGlobalPosition!.dx - temp, _startGlobalPosition!.dy);
    }

    //拖拽（选中列）排序操作
    if (selectedColumnIndexs.isNotEmpty && selectedColumnIndexs.contains(dragColIdx)) {
      //判断是否能拖拽列
      if (!_canDragColumn()) {
        return;
      }

      _isDragColumn = true;

      // 点击选中单元格的x位置
      _dxOfDragColumnBlock = details.localPosition.dx;
      // 选中多个单元格区块的x（相对于选中区块）位置
      // （例如：选中3哥单元格，当点击第三个单元格x=100的位置时，还需要加上前面两个单元格的宽度总和）
      _dxOfDragColumnBlock =
          _dxOfDragColumnBlock! +
          _calculatesFrontOrBehindSelectedColumnsTotalWidthByDragColIdx(dragColIdx, true);
    } else {
      // 框选列操作
      _isPanning = true;
    }

    notifyListeners();
  }

  void columnDragUpdate(
    BuildContext context,
    Offset? tablePosition,
    Size tableSize,
    DragUpdateDetails details,
    int dragColIdx,
  ) {
    // 鼠标开始点击处于冻结区域、并且当前位置处于非冻结区域（说明鼠标垮过区）——鼠标如果一直在冻结区域框选，非冻结区域滚动条不需要滚动；
    // 表格分为冻结区域（左侧）、非冻结区域（右侧）；
    // 当非冻结区域（右侧）x滚动条滚动了一定距离时，鼠标此时如果在冻结区域（左侧）框选，非冻结区域不应该（向左）滚动，只有当鼠标开始从非冻结区域框选至冻结区域时，才触发滚动。
    //鼠标开始位置是否处于非冻结区域
    if (!_autoScrollFlag &&
        _getArea(tablePosition!, _startGlobalPositionOrigina) == AreaOfMouse.frozen &&
        _getArea(tablePosition!, details.globalPosition) == AreaOfMouse.notFrozen) {
      _autoScrollFlag = true;
    }

    /// 鼠标在表格右侧外边
    if (_mouseIsOutside(
      tablePosition,
      tableSize,
      details.globalPosition,
      MousePositionOfTable.rightOutside,
    )) {
      // print('鼠标在表格you侧外----');
      if (_mousePositionOfTable != MousePositionOfTable.rightOutside) {
        _mousePositionOfTable = MousePositionOfTable.rightOutside;

        if (_isPanning || _isDragColumn) {
          _startAutoScroll(context, tablePosition);
        }
      }
    } else if (_mouseIsOutside(
      tablePosition,
      tableSize,
      details.globalPosition,
      MousePositionOfTable.leftOutside,
    )) {
      // print('鼠标在表格zuo侧外----');
      _endGlobalPosition = details.globalPosition;

      if (_isPanning) {
        _handleSelectedColumn(context, tablePosition);
      } else if (selectedColumnIndexs.contains(dragColIdx) && _isDragColumn) {
        _handleMarkInsertIdx(context, tablePosition, _endGlobalPosition, 1);
      }

      notifyListeners();

      if (!_autoScrollFlag &&
          _getArea(tablePosition!, _startGlobalPositionOrigina) == AreaOfMouse.frozen) {
        return;
      }
      if (_mousePositionOfTable != MousePositionOfTable.leftOutside) {
        _mousePositionOfTable = MousePositionOfTable.leftOutside;

        if (_isPanning || _isDragColumn) {
          _startAutoScroll(context, tablePosition, isRightScroll: false);
        }
      }
    } else {
      // print('鼠标在表格内');
      _stopAutoScroll();
      _endGlobalPosition = details.globalPosition;
      if (_isPanning) {
        _handleSelectedColumn(context, tablePosition);
      } else if (_isDragColumn) {
        notifyListeners();

        _handleMarkInsertIdx(context, tablePosition, _endGlobalPosition, 1);
      }
    }
  }

  void columnDragEnd() {
    //如果有匹配到插入的位置
    if (_insertIdx != -1 && selectedColumnIndexs.isNotEmpty) {
      /// 修改选中列的位置
      // 需要移动的起始列索引
      int start = selectedColumnIndexs[0];
      // 需要移动的末尾列索引
      int end = selectedColumnIndexs[selectedColumnIndexs.length - 1] + 1;

      // 计算实际插入位置（右移动时，因为需要先删除移动的列，所以会导致插入位置变化）
      int actualInsertIdx = _insertIdx;
      if (_insertIdx > start) {
        // 向后移动时，需要减去被移动元素的长度
        actualInsertIdx -= (selectedColumnIndexs.length - 1);
        actualInsertIdx -= 1;
      }

      // print('变更前');
      // print(_columns.map((c) => c.field).join('、'));

      List<AppTableColumn> elementsToMove = _columns.sublist(start, end);
      _columns.removeRange(start, end); // 删除索引 2-3 的元素

      //拖拽的列默认为非冻结列
      var _frozen = AppTableColumnFrozen.none;
      //但是如果拖拽到冻结列左侧，都需要变为冻结列
      if (actualInsertIdx < _columns.length) {
        _frozen = _columns[actualInsertIdx].frozen;
      }
      //修改拖拽列的冻结属性
      elementsToMove.forEach((c) {
        c.frozen = _frozen;
      });

      //插入目标位置
      _columns.insertAll(actualInsertIdx, elementsToMove); // 在索引 1 的位置插入元素

      // /// 修改选中列对应的GlobalKey的位置
      // List<GlobalKey> elementsToMoveKeys = _columnsKeys.sublist(start, end);
      // _columnsKeys.removeRange(start, end);
      // _columnsKeys.insertAll(actualInsertIdx, elementsToMoveKeys);

      // print('变更后');
      // print(_columns.map((c) => c.field).join('、'));

      /// 选中排序后的列
      List<int> checkedColumnIdxs = List.generate(
        selectedColumnIndexs.length,
        (index) => index + actualInsertIdx,
      );

      setSelectedColumn(checkedColumnIdxs);
    }

    _autoScrollFlag = false;
    _dxOfDragColumnBlock = null;
    _insertIdx = -1;

    _startGlobalPosition = null;
    _startGlobalPositionOrigina = null;
    _endGlobalPosition = null;
    _mousePositionOfTable = null;
    _isPanning = false;
    _isDragColumn = false;
    _stopAutoScroll();

    _markColumnsChanged();

    notifyListeners();
  }

  // 数值改变表示y轴在滚动
  var _horizontalScrollFlag = 0;

  int get horizontalScrollFlag => _horizontalScrollFlag;

  /// 监听横向滚动条滚动事件（修改插入列提示线位置）
  void onTableHorizontalScroll(double offset) {
    if (_insertX != -1) {
      // 如果需要在滚动时更新其他状态，可以在这里处理
      // 例如更新拖拽提示线的位置等

      WidgetsBinding.instance.addPostFrameCallback((_) {
        _insertX += offset;
        _horizontalScrollFlag++;
        // 通知监听器更新
        notifyListeners();
      });
    } else {
      _horizontalScrollFlag++;
      // 通知监听器更新
      notifyListeners();
    }
  }

  /// 表格行hover—————————————————————————————————————————————————————————————————————————————————————————
  int? _hoverRowIdx;

  int? get hoverRowIdx => _hoverRowIdx;

  void handleHoverRow(int? rowIdx) {
    _hoverRowIdx = rowIdx;
    notifyListeners();
  }

  /// 表格行拖拽排序逻辑—————————————————————————————————————————————————————————————————————————————————————————
  ///
  Offset? _startRowGlobalPosition; // 拖拽行开始时的全局位置（相对于屏幕左上角）
  /// 测试代码
  Offset? _endRowGlobalPosition; // 拖拽行结束时的全局位置（相对于屏幕左上角）

  double? _startRowY; // 拖拽行开始时的x坐标（相对于表格左上角）
  double? _offsetRowY; // 拖拽行x轴移动距离
  double? _dxOfDragRowBlock; // 点击单元哥的位置

  double _insertSplitlineMatcHeight = 10; // 拖拽行时，鼠标位置和行（上下）边框的距离（距离多少表示匹配）
  double? _siblingNodeLineY; // 兄弟节点提示线
  double _siblingLineIndent = 0; // 兄弟节点提示线缩进
  double? _parentNodeY; //匹配到父节点（框）
  String? _parentNodeId; // 匹配到父节点id
  AppTableRowData? _dragRow; //拖拽的行
  String? _autoExpandId = ''; //自动展开节点的id（拖拽节点时，需要收起子节点，拖拽完成，需要还原）

  bool _isPanningRow = false; // 是否正拖拽框选列头
  bool _isDragRow = false; //是否正拖拽选中行（排序）

  double _scrollOffsetY = 0; //Y轴滚动条滚动距离

  Offset? get startRowGlobalPosition => _startRowGlobalPosition;
  Offset? get endRowGlobalPosition => _endRowGlobalPosition;
  double? get startRowY => _startRowY;
  double? get offsetRowY => _offsetRowY;
  double? get siblingNodeLineY => _siblingNodeLineY;
  double get siblingLineIndent => _siblingLineIndent;
  double? get parentNodeY => _parentNodeY;
  String? get autoExpandId => _autoExpandId;

  /// 查找 target 处于哪一个区间对应的索引；
  /// 假设有一个区间的上下边框分别为 50、100 时，当鼠标属于 "50 + _insertSplitlineMatcHeight"和“100 - _insertSplitlineMatcHeight”之间时，返回索引；
  /// 其余区域留出来匹配边框索引（兄弟节点）
  ///
  /// [list] 由行的上下边框y坐标组成
  /// [target] 当前鼠标位置
  int findInterval(List<double> list, double target, {double deviation = 0}) {
    // 如果数组为空或只有一个元素，无法形成区间
    if (list.length < 2) return -1;

    // 特殊情况：目标值小于第一个元素
    if (target < list[0]) return -1;

    // 查找区间
    for (int i = 0; i < list.length - 1; i++) {
      var startOfRow = list[i] + deviation;
      var endOfRow = list[i + 1] - deviation;

      //必须在某个区间内（行）
      var inRange = target >= startOfRow && target <= endOfRow;
      //但是需要排序拖动的区间（行）——自己（行）不能时自己的子节点
      var excludeRange =
          _startRowGlobalPosition != null &&
          !(_startRowGlobalPosition!.dy >= startOfRow && _startRowGlobalPosition!.dy <= endOfRow);

      if (inRange && excludeRange) {
        // print(
        //   '${target}-${list[i] + deviation}-${list[i + 1] - deviation}',
        // );
        return i;
      }
    }

    // 特殊情况：目标值等于或大于最后一个元素
    if (target == list.last) return list.length - 2;

    // 未找到区间
    return -1;
  }

  // /// 根据行索引，获取行的 Y 坐标
  // double? _getYByRowIdx(int rowIdx) {
  //   return rowLinesY[matchParentRowIdx] - tablePosition!.dy;
  // }

  /// 获取行线 Y 坐标（每一行的上下边框y坐标——1行2条坐标，2行3条坐标）
  List<double> getRowsLinesY(Offset? tablePosition) {
    List<double> rowLinesY = [];

    double flag = 0;
    // 第一行的上边框
    if (_flattenedRows.isNotEmpty) {
      flag = tablePosition!.dy + headerHeight;
      rowLinesY.add(flag - verticalScrollController.offset.abs());
      for (int i = 0; i < _flattenedRows.length; i++) {
        flag += rowHeight;
        rowLinesY.add(flag - verticalScrollController.offset.abs());
      }
    }
    return rowLinesY;
  }

  /// 获取指定节点及其所有子节点的最大层级深度
  /// [nodeId] 节点ID
  /// 返回该节点及其所有子节点的最大层级深度
  int _getNodeMaxDepth(String nodeId) {
    // 递归计算节点深度的辅助函数
    int calculateDepth(AppTableRowData node) {
      if (!node.hasChildren || node.children == null || node.children!.isEmpty) {
        return 1; // 叶子节点深度为1
      }

      // 计算所有子节点的最大深度
      int maxChildDepth = 0;
      for (var child in node.children!) {
        int childDepth = calculateDepth(child);
        if (childDepth > maxChildDepth) {
          maxChildDepth = childDepth;
        }
      }

      return 1 + maxChildDepth; // 当前节点深度 = 1 + 子节点最大深度
    }

    // 查找指定节点
    var node = _allRows.firstWhere((row) => row.id == nodeId);

    // 计算深度
    return calculateDepth(node);
  }

  /// 根据行索引，获取行Y坐标（上边框）
  double getYByRowIdx(List<double> rowsLinesY, int rowIdx, Offset? tablePosition) {
    List<double> rowLinesY = rowsLinesY;
    return rowLinesY[rowIdx] - tablePosition!.dy;
  }

  /// 展开节点
  _handleExpandNode(String? nodeId) {
    if (nodeId == null || nodeId.isEmpty) return;
    if (!_flattenedRows.firstWhere((row) => row.id == nodeId).isExpanded) {
      toggleNode(nodeId);
    }
  }

  /// 自动展开节点
  _handleAutoExpandNode() {
    WidgetsBinding.instance.addPostFrameCallback((_) {
      if (_autoExpandId != null) {
        _handleExpandNode(_autoExpandId);
        _setAutoExpandId('');
      }
    });
  }

  //拖拽行完成后，释放变量
  _dragEndReleaseVariable() {
    _offsetRowY = null;
    _dxOfDragRowBlock = null;
    // _insertIdx = -1;

    _parentNodeY = null;
    _parentNodeId = null;

    _siblingNodeLineY = null;

    _startRowGlobalPosition = null;
    _mousePositionOfTable = null;
    _isPanningRow = false;
    _isDragRow = false;

    _dragRow = null;
    _stopAutoScrollVertical();

    // _markColumnsChanged();
    notifyListeners();
  }

  /// 标记匹配的行相关提示线
  void _handleMarkRow(BuildContext context, Offset? tablePosition, Size? tableSize) {
    if (_endRowGlobalPosition != null) {
      //所有显示行的边框
      List<double> rowLinesY = getRowsLinesY(tablePosition);

      /// 一行至少有上下两条边框坐标
      if (rowLinesY.length >= 2) {
        // 匹配到的父节点（行）————当前鼠标停留在第几行

        // 鼠标纵坐标位置
        var _endY = _endRowGlobalPosition!.dy + 1; // （+ 1的作用）假装没有超出表格
        //鼠标在表格上面
        if (_mousePositionOfTable == MousePositionOfTable.topOutside) {
          _endY = tablePosition!.dy + headerHeight;
        } else if (_mousePositionOfTable == MousePositionOfTable.bottomOutside) {
          _endY =
              rowLinesY.where((n) => n < tablePosition!.dy + tableSize!.height).last -
              1; // （- 1的作用）假装没有超出表格
        } else {
          _endY = _endRowGlobalPosition!.dy;
        }

        // if (_mousePositionOfTable == MousePositionOfTable.bottomOutside &&
        //     _endY > rowLinesY[rowLinesY.length - 1]) {
        //   _endY = rowLinesY[rowLinesY.length - 1];
        // }

        var matchParentRowIdx = -1;

        //支持树结构才需要匹配父节点
        if (treeConfig != null) {
          matchParentRowIdx = findInterval(
            rowLinesY,
            _endY, //_endRowGlobalPosition!.dy,
            deviation: _insertSplitlineMatcHeight,
          );

          // 表示匹配到行，拖拽节点当出该行的子节点
          if (matchParentRowIdx > -1) {
            //标记匹配到的父节点
            _parentNodeY = getYByRowIdx(rowLinesY, matchParentRowIdx, tablePosition);

            _parentNodeId = _flattenedRows[matchParentRowIdx].id;

            //清空匹配到的兄弟节点
            _siblingNodeLineY = null;
            return;
          }
        }

        // 匹配兄弟节点
        var matchLineIdx = rowLinesY.indexWhere(
          (element) => (_endY - element).abs() < _insertSplitlineMatcHeight,
        );

        // matchLineIdx > -1 必须匹配到索引，且排除选中的列对应的索引
        if (matchLineIdx > -1
        // &&
        //     (matchLineIdx < selectedColumnIndexs[0] ||
        //         matchLineIdx >
        //             selectedColumnIndexs[selectedColumnIndexs.length - 1] +
        //                 1)
        ) {
          // _insertIdx = matchLineIdx;
          // 清空匹配到的父节点
          _parentNodeY = null;
          _parentNodeId = null;

          //标记匹配到的兄弟节点
          _siblingNodeLineY = getYByRowIdx(rowLinesY, matchLineIdx, tablePosition);

          // rowLinesY[matchLineIdx] - tablePosition!.dy;

          // 匹配到第一行的上边框
          if (matchLineIdx == 0) {
            _siblingLineIndent = 0;
            // print('匹配到最前面');
          } else if (matchLineIdx > 0) {
            //匹配到的节点
            var matchNode = _flattenedRows[matchLineIdx - 1];
            //匹配到节点的下一行节点
            var nextRowIndex = matchLineIdx;
            var nextRowData =
                nextRowIndex < _flattenedRows.length - 1 ? getRowData(nextRowIndex) : null;
            var nextRowLevel = nextRowData != null ? nextRowData.level - 1 : 0;

            /// 层级差（叶子节点（只考虑展开的节点）该值大于0）
            /// 叶子节点，看他下一个节点缩进几级（例如一组里面最深节点为5级，它下一个展开的节点为3级（3 - 1），那么需要缩进（5-2）级）；
            var levelDiff = matchNode.level - nextRowLevel;

            //如果是一组里面最后一条记录
            if (isLastVisibleInGroup(matchLineIdx - 1)) {
              levelDiff = matchNode.level + 1;
            }

            //开始缩进的位置
            var firstMatchX =
                (matchNode.level - levelDiff) * indentSize + (levelDiff == 0 ? 0 : indentSize);
            //缩进需要考虑索引列
            var temp = indexColumnWidth + firstMatchX;

            List<double> indents = [];
            for (var i = 0; i <= levelDiff + (levelDiff == 0 ? 1 : 0); i++) {
              //如果是匹配的第一段，且没有缩进，需要把整条线拉动到盖着整行
              if (i == 0 && firstMatchX == 0) {
                indents.add(temp - indexColumnWidth);
              } else {
                indents.add(temp);
              }
              temp += indentSize;
            }

            //如果鼠标在缩进的左侧，匹配最小的缩进
            var targetValue = _endRowGlobalPosition!.dx - tablePosition!.dx;
            //匹配真实的缩进
            var index =
                targetValue < indents[0]
                    ? 0
                    : targetValue > indents[indents.length - 1]
                    ? indents.length - 1
                    : findInterval(indents, targetValue);

            if (index > -1) {
              _siblingLineIndent = indents[index];
            }

            //提示线条缩进位置小于索引列右侧位置——只需要确认插入位置（一级节点排序）
            if (_siblingLineIndent == 0 || treeConfig == null) {
              // print('一级节点排序');
              _siblingLineIndent = 0;
            } else {
              // print('需要确认父节点，再确认兄弟节点');

              var nodeIndex = indents.reversed.toList().indexWhere((y) => y == _siblingLineIndent);

              //父节点是匹配到的节点
              if (nodeIndex == 0) {
                _parentNodeY = getYByRowIdx(rowLinesY, matchLineIdx - 1, tablePosition);

                _parentNodeId = _flattenedRows[matchLineIdx - 1].id;
              } else {
                var tempCurrentNode = matchNode;
                for (var i = 0; i < nodeIndex; i++) {
                  tempCurrentNode = _flattenedRows.firstWhere(
                    (n) => n.id == tempCurrentNode.parentId,
                  );
                }

                var idxOfParentRow = _flattenedRows.indexWhere(
                  (row) => row.id == tempCurrentNode.id,
                );
                _parentNodeY = getYByRowIdx(rowLinesY, idxOfParentRow, tablePosition);

                _parentNodeId = _flattenedRows[idxOfParentRow].id;

                // print(tempCurrentNode.data);
              }
            }
          }

          notifyListeners();
        }
      }
    }
  }

  // 数值改变表示y轴在滚动
  var _verticalScrollFlag = 0;

  int get verticalScrollFlag => _verticalScrollFlag;

  void onTableVerticalScroll() {
    _verticalScrollFlag++;
    notifyListeners();
  }

  void rowDragStart(
    Offset? tablePosition,
    DragStartDetails details,
    int dragRowIdx,
    BuildContext context,
  ) {
    _scrollOffsetY = verticalScrollController.offset;

    //记录鼠标开始拖拽的位置
    _startRowGlobalPosition = details.globalPosition;

    // //拖拽（选中列）排序操作
    // if (selectedColumnIndexs.isNotEmpty &&
    //     selectedColumnIndexs.contains(dragColIdx)) {
    _isDragRow = true;

    _dragRow = _flattenedRows[dragRowIdx];

    // // //不是叶子节点（有子节点），并且是展开状态，收起节点，因为拖拽时，不能拖拽到当前节点的子节点
    if (_dragRow!.hasChildren && _dragRow!.isExpanded) {
      _setAutoExpandId(_dragRow!.id);
      toggleNode(_dragRow!.id);
    }

    // 点击选中单元格的x位置
    _dxOfDragRowBlock = details.localPosition.dy;
    // 选中多个单元格区块的x（相对于选中区块）位置
    // // （例如：选中3哥单元格，当点击第三个单元格x=100的位置时，还需要加上前面两个单元格的宽度总和）
    // _dxOfDragRowBlock =
    //     _dxOfDragRowBlock! +
    //     _calculatesFrontOrBehindSelectedColumnsTotalWidthByDragColIdx(
    //       dragColIdx,
    //       true,
    //     );

    //所有选中列中，第一列的x坐标（相对于表格左上角）
    _startRowY = (details.globalPosition.dy - tablePosition!.dy) - _dxOfDragRowBlock!;
    // } else {
    //   // 框选列操作
    //   _isPanning = true;
    // }

    notifyListeners();
  }

  void rowDragUpdate(
    BuildContext context,
    Offset? tablePosition,
    Size tableSize,
    DragUpdateDetails details,
    int dragRowIdx,
  ) {
    /// 鼠标在表格右侧外边
    if (_mouseIsOutside(
      tablePosition,
      tableSize,
      details.globalPosition,
      MousePositionOfTable.topOutside,
    )) {
      // // print('鼠标在表格上侧外----');
      if (_mousePositionOfTable != MousePositionOfTable.topOutside) {
        _mousePositionOfTable = MousePositionOfTable.topOutside;

        if (_isPanningRow || _isDragRow) {
          _startAutoScrollVertical(context, tablePosition, tableSize);
        }
      }
    } else if (_mouseIsOutside(
      tablePosition,
      tableSize,
      details.globalPosition,
      MousePositionOfTable.bottomOutside,
    )) {
      // // print('鼠标在表格下侧外----');
      if (_mousePositionOfTable != MousePositionOfTable.bottomOutside) {
        _mousePositionOfTable = MousePositionOfTable.bottomOutside;

        if (_isPanningRow || _isDragRow) {
          _startAutoScrollVertical(context, tablePosition, tableSize, isTopScroll: false);
        }
      }
    } else {
      // print('鼠标在表格内');
      _mousePositionOfTable = MousePositionOfTable.inside;
      _stopAutoScrollVertical();
      _endRowGlobalPosition = details.globalPosition;

      if (_isPanningRow) {
        // _handleSelectedColumn(context);
      } else if (_isDragRow) {
        // /// 拖拽选中列时，修改选中列提示块的位置
        // if (selectedColumnIndexs.contains(dragRowIdx)) {
        // 计算移动距离
        var movingdistance =
            _startRowY! - (details.globalPosition.dy - tablePosition!.dy) + _dxOfDragRowBlock!;

        _offsetRowY = movingdistance;

        // }
        notifyListeners();

        _handleMarkRow(context, tablePosition, tableSize);

        // _handleMarkInsertIdx(context, tablePosition);
      }
    }
    // print('update-----');
  }

  void rowDragEnd(Offset? tablePosition) {
    if (_dragRow == null) {
      _handleAutoExpandNode();
      _dragEndReleaseVariable();
      return;
    }

    //拖拽行id
    var dragNodeId = _dragRow!.id;

    // 拖拽行索引
    var dragNodeIdx = _flattenedRows.indexWhere((row) => row.id == dragNodeId);

    // 标记是否需要重建树结构
    bool needRebuildTree = false;

    //只匹配了父节点（那么当前节点+1就是兄弟节点的索引）
    if (_siblingNodeLineY == null && _parentNodeId != null) {
      var matchLineIdx = _flattenedRows.indexWhere((n) => n.id == _parentNodeId);

      _siblingNodeLineY = getYByRowIdx(getRowsLinesY(tablePosition), matchLineIdx, tablePosition);
    }
    //如果有匹配到上下边框
    if (_siblingNodeLineY != null) {
      //索引行对应的边框
      List<double> rowLinesY = getRowsLinesY(tablePosition);
      //匹配的线
      double matchLineY = _siblingNodeLineY! + tablePosition!.dy;
      //拖拽行匹配到的边框索引
      var matchLineIdx = rowLinesY.indexWhere((l) => l == matchLineY);

      // ——————————————————树结构————bug临时修复，拖动到最后，匹配线会比最后一行高2，导致匹配失败
      if (matchLineIdx == -1 && matchLineY + 2 == rowLinesY.last) {
        matchLineIdx = rowLinesY.length - 1;
      }
      // print(
      //   '——————————————————————————————————————matchLineIdx 22222==${matchLineIdx} —— ${_siblingNodeLineY! + tablePosition!.dy} —— ${rowLinesY}',
      // );

      // 更改（拖拽节点）父节点（树结构才能修改父节点）
      // 父节点不能是自己
      // treeConfig != null 必须支持树结构
      // dragNodeId != _parentNodeId 父节点不能是当前节点
      if (treeConfig != null && dragNodeId != _parentNodeId) {
        // 保存旧的父节点ID
        String? oldParentId = _dragRow!.parentId;

        // _parentNodeId 为空时，表示拖拽的节点改为1级节点（表示没有父节点）
        //父节点处于层级
        var parentRowLevel =
            _parentNodeId != null
                ? _flattenedRows.firstWhere((r) => r.id == _parentNodeId).level + 1
                : 0;
        //当前节点一共有多少层
        var currentDeep = _getNodeMaxDepth(_dragRow!.id);

        //如果有层级限制
        if (maxLevel != null && parentRowLevel + currentDeep <= maxLevel!) {
          try {
            // 更新节点的父ID
            _dragRow!.parentId = _parentNodeId;
            _dragRow!.data[treeConfig!.parentIdField] = _parentNodeId;
            if (_parentNodeId == null) {
              _dragRow!.level = 0;
            }
          } catch (e) {
            // print(_dragRow!.data);
            // print(treeConfig!.parentIdField);
            // print(_parentNodeId);

            // for (var i = 0; i < _allRows.length; i++) {
            //   var obj = _allRows[i];
            //   print('${obj.data.keys.join("、")}');
            //   print('${obj.data.values.join("、")}');
            // }
          }

          // 标记需要重建树结构
          needRebuildTree = true;
        } else {
          showMessage('超出层级限制');
          _dragEndReleaseVariable();
          return;
          // Tooltip(message: "超出层级限制", child: Icon(Icons.info));
        }
      }

      //更改拖拽节点排序
      //拖拽行所对应的边框（上下边框）索引（每一行对应两条边框索引）
      var dragNodeLinesIdx = [dragNodeIdx, dragNodeIdx + 1];

      //拖拽行匹配到的边框索引是当前行的上下边框索引————意味着不需要改变排序
      if (dragNodeLinesIdx.contains(matchLineIdx)) {
        // print('不改变排序');
      } else {
        //是否向上拖拽
        var isUp = matchLineIdx < dragNodeIdx;

        //需要改变位置的行索引
        var delIdx = _allRows.indexWhere((r) => r.id == dragNodeId);

        //删除需要改变的行
        _allRows.removeAt(delIdx);

        //插入末尾位置
        if (matchLineIdx == rowLinesY.length - 1) {
          _allRows.add(_dragRow!);
        } else {
          // 插入指定位置
          //需要插入的位置
          if (matchLineIdx > -1) {
            var insertNodeId = _flattenedRows[matchLineIdx].id;

            //对应到_allRows数组中的索引
            var insertNodeIdx = _allRows.indexWhere((r) => r.id == insertNodeId);

            _allRows.insert(insertNodeIdx, _dragRow!);
          }
        }

        // 标记需要重建树结构
        needRebuildTree = true;
      }
    }

    // 只在需要时重建树结构，避免重复操作
    if (needRebuildTree) {
      _allRows = convertToTreeData(_allRows.map((row) => row.data).toList());
      _setAutoExpandId('');
    } else {
      //没有重新构建数，需要自动展开因拖拽而收起的节点
      _handleAutoExpandNode();
    }

    _initializeVisibleRows();

    _markRowsChanged();

    _dragEndReleaseVariable();

    // notifyListeners();
  }

  bool _hoverAddRowButton = false;
  bool _hoverAddColumnButton = false;

  bool get hoverAddRowButton => _hoverAddRowButton;
  bool get hoverAddColumnButton => _hoverAddColumnButton;

  void setHoverAddRowButton(bool isHover) {
    _hoverAddRowButton = isHover;
    notifyListeners();
  }

  void setHoverAddColumnButton(bool isHover) {
    _hoverAddColumnButton = isHover;
    notifyListeners();
  }

  AppTableRowData newRow({String? parentId, List<AppTableColumn>? cols}) {
    final String newId = _uuid.v4();

    // 构造新行数据
    final Map<String, dynamic> newRowMap = <String, dynamic>{
      // 其它字段默认空
      for (var col in cols!) col.field: col.type.defaultValue,
    };

    //确定扁平节点或者树结构的主键
    newRowMap[treeConfig == null ? uniqueId : treeConfig!.idField] = newId;

    if (treeConfig == null) {
      return AppTableRowData(
        data: newRowMap,
        level: 0,
        hasChildren: false,
        parentId: '',
        id: newId,
        isExpanded: false,
        children: null,
        levelOneIndex: null,
        childrenCount: 0,
        descendantsCount: 0,
        isLeaf: true,
      );
    } else {
      // 树结构
      final String? parent = parentId ?? treeConfig!.emptyParentValue;
      newRowMap[treeConfig!.parentIdField] = parent;

      // 计算level
      int level = 0;
      if (parent != null) {
        // 找到父节点
        final parentNode = _allRows.firstWhere(
          (row) => row.id == parent,
          orElse: () => throw Exception('Parent not found'),
        );
        level = parentNode.level + 1;
        parentNode.hasChildren = true;
        parentNode.isLeaf = false;
        parentNode.children ??= [];
      }

      final newRowData = AppTableRowData(
        data: newRowMap,
        level: level,
        hasChildren: false,
        parentId: parent,
        id: newId,
        isExpanded: false,
        children: null,
        levelOneIndex: null,
        childrenCount: 0,
        descendantsCount: 0,
        isLeaf: true,
      );

      return newRowData;
    }
  }

  void clearRows() {
    _flattenedRows.clear();
    _allRows.clear();
    _initializeVisibleRows();

    _markRowsChanged();
    notifyListeners();
  }

  void clearColumns() {
    _columns.clear();
    _markRowsChanged();
    notifyListeners();
  }

  void clear() {
    clearRows();
    clearColumns();
    notifyListeners();
  }

  // /// 添加列后的滚动位置调整逻辑
  // /// [tableViewportWidth] 当前表格的可视区域宽度
  // /// [columnWidth] 新添加列的宽度
  // void adjustScrollPositionAfterAddColumn(
  //   double tableViewportWidth,
  //   double columnWidth,
  // ) {
  //   /// 避免添加列后，把添加列按钮顶出可视区域
  //   if (tableViewportWidth <
  //       tableContentWidth + horizontalScrollController.offset) {
  //     horizontalScrollController.jumpTo(
  //       horizontalScrollController.offset + columnWidth,
  //     );
  //   }
  // }

  String? get editRowId => _editRowId;
  String? get editColumnId => _editColumnId;

  String? _editRowId;
  String? _editColumnId;
  void setEditCell({String? rowId, String? columnId}) {
    _editRowId = rowId;
    _editColumnId = columnId;
    notifyListeners();
  }

  ////////___________添加、编辑列相关____________________________________________________________________________________

  AppTableColumn? columnModel;
  String columnStatus = 'add';

  ColumnTypeEnum get currentColumnTypeId =>
      columnModel != null ? columnModel!.type.typeCode : ColumnTypeEnum.text;

  /// 是否为文本
  bool get isText => columnModel != null ? columnModel!.type is AppTableColumnTypeText : false;

  /// 是否为数字
  bool get isNumber => columnModel != null ? columnModel!.type is AppTableColumnTypeNumber : false;

  // 是否为货币
  bool get isCurrency =>
      columnModel != null ? columnModel!.type is AppTableColumnTypeCurrency : false;

  /// 是否为百分比
  bool get isPercentage =>
      columnModel != null ? columnModel!.type is AppTableColumnTypePercentage : false;

  /// 是否为日期
  bool get isDate => columnModel != null ? columnModel!.type is AppTableColumnTypeDate : false;

  /// 是否为单选
  bool get isSingleSelect =>
      columnModel != null
          ? columnModel!.type is AppTableColumnTypeSingleSelect &&
              (columnModel!.type as AppTableColumnTypeSingleSelect).subType == SelectEnum.single
          : false;

  /// 是否为多选
  bool get isMultipleSelect =>
      columnModel != null
          ? columnModel!.type is AppTableColumnTypeSingleSelect &&
              (columnModel!.type as AppTableColumnTypeSingleSelect).subType == SelectEnum.multiple
          : false;

  /// 是否为电话号码
  bool get isTelephone =>
      columnModel != null ? columnModel!.type is AppTableColumnTypeTelephone : false;

  /// 是否为复选框
  bool get isCheckbox =>
      columnModel != null ? columnModel!.type is AppTableColumnTypeCheckbox : false;

  /// 数据精度
  List<GeneralOption<int>> _numberTypes = [];
  List<GeneralOption<int>> get numberTypes => _numberTypes;

  void _initNumberTypes() {
    if (!(isNumber || isPercentage || isCurrency)) {
      return;
    }

    int numFlag = 1234;
    if (isPercentage) {
      numFlag = 12;
    } else if (isCurrency) {
      numFlag = 1;
    }

    _numberTypes = List.generate(5, (index) {
      return GeneralOption<int>(
        id: index,
        text: AppTableGeneralHelper.getNumberFormatStr(
          numFlag,
          isShowPercentiles: isNumber ? isShowPercentiles : false,
          precision: index,
          isRetainDecimal: true,
          suffix: isPercentage ? '%' : '',
        ),
      );
    });
  }

  // 日期格式
  List<GeneralOption<int>> _dateTypes = [];
  List<GeneralOption<int>> get dateTypes => _dateTypes;

  void _initDateTypes() {
    if (!(isDate)) {
      return;
    }
    var now = DateTime.now();

    var dateFormats = AppTableCommon.dateFormats;
    // 使用 List.generate 方法
    _dateTypes = List.generate(dateFormats.length, (index) {
      var format =
          AppTableCommon.getDateFormatOption(index + 1).text +
          (showWeek ? ' EEEE' : '') +
          (showTime ? ' HH:mm' : '');
      var text = DateFormat(format, 'zh').format(now);
      return GeneralOption<int>(id: index + 1, text: text);
    });
  }

  // 是否显示列描述
  bool _showDescInput = false;
  bool get showDescInput => _showDescInput;
  void setShowDescInput(bool value) {
    _showDescInput = value;
    notifyListeners();
  }

  void toggleShowDescInput() {
    _showDescInput = !_showDescInput;
    notifyListeners();
  }

  // 是否显示千分位
  // bool _showPercentiles = false;
  /// 是否显示千分位
  bool get isShowPercentiles =>
      (columnModel != null && columnModel!.type is AppTableColumnTypeNumber)
          ? (columnModel!.type as AppTableColumnTypeNumber).isShowPercentiles
          : false;

  void setShowPercentiles(bool value) {
    // _showPercentiles = value;
    if (columnModel != null && columnModel!.type is AppTableColumnTypeNumber) {
      (columnModel!.type as AppTableColumnTypeNumber).isShowPercentiles = value;

      _initNumberTypes();
      notifyListeners();
    }
  }

  //是否默认勾选
  bool get isDefaultChecked =>
      (columnModel != null && columnModel!.type is AppTableColumnTypeCheckbox)
          ? (columnModel!.type as AppTableColumnTypeCheckbox).defaultValue
          : false;

  void setDefaultChecked(bool value) {
    if (columnModel != null && columnModel!.type is AppTableColumnTypeCheckbox) {
      (columnModel!.type as AppTableColumnTypeCheckbox).defaultValue = value;
      notifyListeners();
    }
  }

  /// 设置为保存必填项
  bool get isSaveRequired =>
      columnModel != null && columnModel!.type is AppTableColumnTypeWithRequestField
          ? (columnModel!.type as AppTableColumnTypeWithRequestField).isSaveRequired
          : false;

  void setSaveRequired(bool value) {
    if (columnModel != null && columnModel!.type is AppTableColumnTypeWithRequestField) {
      if (columnModel!.type is AppTableColumnTypeText) {
        (columnModel!.type as AppTableColumnTypeText).isSaveRequired = value;
      } else if (columnModel!.type is AppTableColumnTypeNumber) {
        (columnModel!.type as AppTableColumnTypeNumber).isSaveRequired = value;
      } else if (columnModel!.type is AppTableColumnTypePercentage) {
        (columnModel!.type as AppTableColumnTypePercentage).isSaveRequired = value;
      } else if (columnModel!.type is AppTableColumnTypeCurrency) {
        (columnModel!.type as AppTableColumnTypeCurrency).isSaveRequired = value;
      }
      notifyListeners();
    }
  }

  /// 设置为提交审批必填项
  bool get isSubmitRequired =>
      columnModel != null && columnModel!.type is AppTableColumnTypeWithRequestField
          ? (columnModel!.type as AppTableColumnTypeWithRequestField).isSubmitRequired
          : false;

  void setSubmitRequired(bool value) {
    if (columnModel != null && columnModel!.type is AppTableColumnTypeWithRequestField) {
      if (columnModel!.type is AppTableColumnTypeText) {
        (columnModel!.type as AppTableColumnTypeText).isSubmitRequired = value;
      } else if (columnModel!.type is AppTableColumnTypeNumber) {
        (columnModel!.type as AppTableColumnTypeNumber).isSubmitRequired = value;
      } else if (columnModel!.type is AppTableColumnTypePercentage) {
        (columnModel!.type as AppTableColumnTypePercentage).isSubmitRequired = value;
      } else if (columnModel!.type is AppTableColumnTypeCurrency) {
        (columnModel!.type as AppTableColumnTypeCurrency).isSubmitRequired = value;
      }
      notifyListeners();
    }
  }

  // 精度（number、currency、percentage）
  int get precision =>
      columnModel != null && columnModel!.type is AppTableColumnTypeWithNumberFormat
          ? (columnModel!.type as AppTableColumnTypeWithNumberFormat).precision
          : 0;

  void changePrecision(int value) {
    // if (columnModel != null && columnModel!.type is AppTableColumnTypeNumber) {
    //   (columnModel!.type as AppTableColumnTypeNumber).precision = value;
    //   notifyListeners();
    // }
    if (columnModel != null && columnModel!.type is AppTableColumnTypeWithNumberFormat) {
      // 需要根据具体类型来设置，因为 mixin 中的是抽象属性
      if (columnModel!.type is AppTableColumnTypeNumber) {
        (columnModel!.type as AppTableColumnTypeNumber).precision = value;
      } else if (columnModel!.type is AppTableColumnTypePercentage) {
        (columnModel!.type as AppTableColumnTypePercentage).precision = value;
      } else if (columnModel!.type is AppTableColumnTypeCurrency) {
        (columnModel!.type as AppTableColumnTypeCurrency).precision = value;
      }
      notifyListeners();
    }
  }

  /// 货币类型
  int get currencyType =>
      columnModel != null && columnModel!.type is AppTableColumnTypeCurrency
          ? (columnModel!.type as AppTableColumnTypeCurrency).currencyType
          : 1;
  void changeCurrencyType(int value) {
    if (columnModel != null && columnModel!.type is AppTableColumnTypeCurrency) {
      (columnModel!.type as AppTableColumnTypeCurrency).currencyType = value;
      notifyListeners();
    }
  }

  /// 获取日期格式
  int get dateType =>
      columnModel != null && columnModel!.type is AppTableColumnTypeDate
          ? (columnModel!.type as AppTableColumnTypeDate).dateType
          : 1;

  void changeDateType(int value) {
    if (columnModel != null && columnModel!.type is AppTableColumnTypeDate) {
      (columnModel!.type as AppTableColumnTypeDate).dateType = value;
      notifyListeners();
    }
  }

  /// 是否显示星期
  bool get showWeek =>
      columnModel != null && columnModel!.type is AppTableColumnTypeDate
          ? (columnModel!.type as AppTableColumnTypeDate).showWeek
          : false;

  void changeShowWeek(bool value) {
    if (columnModel != null && columnModel!.type is AppTableColumnTypeDate) {
      (columnModel!.type as AppTableColumnTypeDate).showWeek = value;
      _initDateTypes();
      notifyListeners();
    }
  }

  // 是否显示时间
  bool get showTime =>
      columnModel != null && columnModel!.type is AppTableColumnTypeDate
          ? (columnModel!.type as AppTableColumnTypeDate).showTime
          : false;

  void changeShowTime(bool value) {
    if (columnModel != null && columnModel!.type is AppTableColumnTypeDate) {
      (columnModel!.type as AppTableColumnTypeDate).showTime = value;
      _initDateTypes();
      notifyListeners();
    }
  }

  /// 时间默认值类型
  int get defaultValueType =>
      columnModel != null && columnModel!.type is AppTableColumnTypeDate
          ? (columnModel!.type as AppTableColumnTypeDate).defaultValueType
          : 1;
  void chagneDefaultValueType(int value) {
    if (columnModel != null && columnModel!.type is AppTableColumnTypeDate) {
      (columnModel!.type as AppTableColumnTypeDate).defaultValueType = value;
      (columnModel!.type as AppTableColumnTypeDate).defaultValue = '';
      notifyListeners();
    }
  }

  // 单选、多选、下拉框选项
  var optionsChangeFlag = 1; // 选择项集合改变标识
  UnmodifiableListView<GeneralOption<String>> get options => UnmodifiableListView(
    columnModel != null && columnModel!.type is AppTableColumnTypeSingleSelect
        ? (columnModel!.type as AppTableColumnTypeSingleSelect).options
        : [],
  );

  // 添加option
  void addOption() {
    var optionId = _uuid.v4();
    var option = GeneralOption(id: optionId, text: '');
    (columnModel!.type as AppTableColumnTypeSingleSelect).options.add(option);
    optionsChangeFlag++;
    notifyListeners();
  }

  // 删除option
  void removeOption(GeneralOption option) {
    (columnModel!.type as AppTableColumnTypeSingleSelect).options.remove(option);
    optionsChangeFlag++;
    notifyListeners();
  }

  // 改变option的顺序
  void orderOption(int oldIndex, int newIndex) {
    var options = (columnModel!.type as AppTableColumnTypeSingleSelect).options;
    if (newIndex > oldIndex) newIndex -= 1;
    final item = options.removeAt(oldIndex);
    options.insert(newIndex, item);
    optionsChangeFlag++;
    notifyListeners();
  }

  int get selDefaultValueType =>
      columnModel != null && columnModel!.type is AppTableColumnTypeSingleSelect
          ? (columnModel!.type as AppTableColumnTypeSingleSelect).defaultValueType
          : 1;
  void changeSelDefaultValueType(int type) {
    if (columnModel != null && columnModel!.type is AppTableColumnTypeSingleSelect) {
      (columnModel!.type as AppTableColumnTypeSingleSelect).defaultValueType = type;
      (columnModel!.type as AppTableColumnTypeSingleSelect).defaultValue = null;
      notifyListeners();
    }
  }

  // 编辑列显示弹框
  void showEditColumn(String field) {
    if (field.isNotEmpty) {
      var column = AppTableGeneralHelper.newColumn(type: ColumnTypeEnum.text);
      columnModel = column;

      var colObj = _columns.firstWhereOrNull((element) => element.field == field);
      if (colObj != null) {
        columnStatus = 'edit';

        //深拷贝列对象
        var columnTemp = AppTableColumn.fromJson(colObj.toJson());

        columnModel = columnTemp;
        _initNumberTypes();
        _initDateTypes();
        notifyListeners();
      }
    }
  }

  //新增列显示弹框
  void showAddColumn() {
    var column = AppTableGeneralHelper.newColumn(type: ColumnTypeEnum.text);
    columnStatus = 'add';
    columnModel = column;
    _initNumberTypes();
    _initDateTypes();
    notifyListeners();
  }

  void changeColumnType(ColumnTypeEnum columnType) {
    if (columnModel != null) {
      // columnModel!.typeCode = columnType;
      // columnModel!.type = columnType;

      var oldColumnType = columnModel!.type.typeCode;
      if (oldColumnType != columnType) {
        AppTableGeneralHelper.showMessage('修改列改变类型，以添加数据将被清空');
      }

      var oldId = columnModel!.field;
      var oldText = columnModel!.text;

      columnModel = AppTableGeneralHelper.newColumn(type: columnType);
      columnModel!.field = oldId;
      columnModel!.text = oldText;
      // columnModel!.typeCode = columnType;

      _initNumberTypes();
      _initDateTypes();
      notifyListeners();
    }
  }

  /// 新增、编辑时，设置当前操作的列对象模型
  // void setColumnModel(AppTableColumn? colObj) {
  //   //新增
  //   if (colObj == null) {
  //     changeColumnType(ColumnTypeEnum.text);
  //   } else {
  //     //编辑
  //     columnModel = colObj;
  //     notifyListeners();
  //   }
  // }

  ////////___________添加、编辑列相关___________________________________________________________________end___________

  ////////___________多选、单选行相关____________________________________________________________________________________
  /// 当前已选中的行
  // List<AppTableRowData> _selectRows = [];
  // List<AppTableRowData> get selectRows => _selectRows;
  // void setSelectRow(int rowIndex) {
  //   if (rowIndex < 0) {
  //     return;
  //   }
  //   if (rowIndex >= _flattenedRows.length) {
  //     return;
  //   }

  //   var currentCheckedRow = _flattenedRows[rowIndex];
  //   //如果已经存在
  //   if (_selectRows.map((c) => c.id).contains(currentCheckedRow.id)) {
  //     _selectRows.removeWhere((c) => c.id == currentCheckedRow.id);
  //   } else {
  //     _selectRows.add(currentCheckedRow);
  //   }
  //   _markRowsChanged();
  //   notifyListeners();
  // }

  CheckedStatus _tableCheckStatus = CheckedStatus.none;

  /// 表格列头全选按钮状态
  CheckedStatus get tableCheckStatus => _tableCheckStatus;

  /// 获取所有选中的行
  List<AppTableRowData> get checkedRows => _allRows.where((r) => r.isChecked).toList();

  // 更新表格选中状态的方法
  void _updateTableCheckStatus() {
    // print(_allRows.map((r) => r.isChecked).join(','));
    // print('当前状态：${_tableCheckStatus}');
    if (_allRows.isEmpty) {
      _tableCheckStatus = CheckedStatus.none;
    } else if (_allRows.every((row) => row.isChecked)) {
      _tableCheckStatus = CheckedStatus.all;
    } else if (_allRows.every((row) => !row.isChecked)) {
      _tableCheckStatus = CheckedStatus.none;
    } else {
      _tableCheckStatus = CheckedStatus.indeterminate;
    }
    // print('调整后状态：${_tableCheckStatus}');
    notifyListeners();
  }

  void setTableChecked() {
    // if (_tableCheckStatus == CheckedStatus.all ||
    //     _tableCheckStatus == CheckedStatus.indeterminate) {
    //   _tableCheckStatus = CheckedStatus.none;
    // } else {
    //   _tableCheckStatus = CheckedStatus.all;
    // }
    // notifyListeners();

    bool shouldCheckAll;

    // 根据当前状态决定是全选还是全不选
    if (_tableCheckStatus == CheckedStatus.all ||
        _tableCheckStatus == CheckedStatus.indeterminate) {
      shouldCheckAll = false; // 当前是全选状态，点击后应该全不选
    } else {
      shouldCheckAll = true; // 当前不是全选状态，点击后应该全选
    }

    // 更新所有行的选中状态
    for (final row in _allRows) {
      row.isChecked = shouldCheckAll;
    }

    // 更新表格选中状态
    _tableCheckStatus = shouldCheckAll ? CheckedStatus.all : CheckedStatus.none;

    _markRowsChanged();
    notifyListeners();
  }

  /// 设置行选中状态（支持级联选中子节点）
  void setCheckedRow(AppTableRowData rowData, {bool? isChecked}) {
    //如果时多选
    var isMult = checkType == TableCheckedEnum.multiple;
    //多选需要级联
    bool cascade = isMult;

    // 如果没有明确指定选中状态，则切换当前状态
    isChecked ??= !rowData.isChecked;

    // 如果是单选模式，需要先清空其他行的选中状态
    if (checkType == TableCheckedEnum.single) {
      for (final row in _allRows) {
        if (row.id != rowData.id) {
          _updateRowCheckedStatus(row.id, false);
        }
      }
    }

    // 更新当前行的选中状态
    _updateRowCheckedStatus(rowData.id, isChecked);

    // 如果需要级联操作且该节点有子节点
    if (cascade && rowData.hasChildren) {
      // 递归处理所有子节点
      _setAllDescendantsChecked(rowData.id, isChecked);
    }

    // // 向上更新父节点状态（如果所有子节点都被选中，则父节点也应被选中）
    // if (cascade && rowData.parentId != null) {
    //   _updateParentCheckedStatus(rowData.parentId!);
    // }

    // 更新表格整体选中状态
    _updateTableCheckStatus();

    _markRowsChanged();
    notifyListeners();
  }

  /// 全选/取消全选所有行
  void setSelectAllRows(bool isChecked) {
    for (final row in _flattenedRows) {
      row.isChecked = isChecked;
    }

    // 更新表格选中状态
    _tableCheckStatus = isChecked ? CheckedStatus.all : CheckedStatus.none;

    _markRowsChanged();
    notifyListeners();
  }

  /// 更新单行的选中状态
  void _updateRowCheckedStatus(String rowId, bool isChecked) {
    final row = _flattenedRows.firstWhere((r) => r.id == rowId);
    row.isChecked = isChecked;
  }

  /// 递归设置所有后代节点的选中状态
  void _setAllDescendantsChecked(String parentId, bool isChecked) {
    final children = _flattenedRows.where((r) => r.parentId == parentId).toList();

    for (final child in children) {
      _updateRowCheckedStatus(child.id, isChecked);

      // 递归处理子节点的子节点
      if (child.hasChildren) {
        _setAllDescendantsChecked(child.id, isChecked);
      }
    }
  }

  /// 更新父节点选中状态（根据子节点选中情况）
  // void _updateParentCheckedStatus(String parentId) {
  //   final parent = _flattenedRows.firstWhere((r) => r.id == parentId);

  //   // 检查该父节点的所有直接子节点是否都被选中
  //   final children =
  //       _flattenedRows.where((r) => r.parentId == parentId).toList();

  //   if (children.isNotEmpty) {
  //     // 如果所有子节点都被选中，则父节点也被选中
  //     // 如果所有子节点都未被选中，则父节点也未被选中
  //     // 如果部分子节点被选中，则父节点为部分选中状态（如果支持的话）
  //     final allChecked = children.every((child) => child.isChecked);
  //     final allUnchecked = children.every((child) => !child.isChecked);

  //     if (allChecked) {
  //       _updateRowCheckedStatus(parentId, true);
  //     } else if (allUnchecked) {
  //       _updateRowCheckedStatus(parentId, false);
  //     }
  //     // 如果部分选中，可以保持当前状态或者根据需求处理
  //   }

  //   // 继续向上更新祖先节点
  //   if (parent.parentId != null) {
  //     _updateParentCheckedStatus(parent.parentId!);
  //   }
  // }

  // /// 获取所有选中的行
  // List<AppTableRowData> getCheckedRows() {
  //   return _flattenedRows.where((r) => r.isChecked).toList();
  // }

  // /// 检查是否所有行都被选中
  // bool get isAllRowsChecked {
  //   return _flattenedRows.isNotEmpty &&
  //       _flattenedRows.every((r) => r.isChecked);
  // }

  ////////___________多选、单选行相关___________________________________________________________________end___________

  // 在组件销毁时确保停止滚动
  @override
  void dispose() {
    _stopAutoScroll();
    _stopAutoScrollVertical();
    super.dispose();
  }
}
