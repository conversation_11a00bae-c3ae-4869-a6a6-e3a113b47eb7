import 'package:flutter/material.dart';
import 'dart:async';

/// 下拉菜单位置
enum TooltipPlacement {
  topLeft, // 左上方
  topCenter, // 顶部居中
  topRight, // 右上方
  rightTop, // 右上方
  rightCenter, // 右侧居中
  rightBottom, // 右下方
  bottomLeft, // 左下方
  bottomCenter, // 底部居中
  bottomRight, // 右下方
  leftTop, // 左上方
  leftCenter, // 左侧居中
  leftBottom, // 左下方
}

/// 提示框显示动画类型
enum TooltipAnimationType {
  /// 淡入动画
  fadeIn,

  /// 缩放动画
  scaleIn,
}

// 自定义控制器类
class AppCustomTooltipController {
  // 控制器需要保存组件状态的引用
  _AppCustomTooltipState? _state;

  // 注册组件状态
  void _registerState(_AppCustomTooltipState state) {
    _state = state;
  }

  // 释放组件状态
  void dispose() {
    _state = null;
  }

  void showTooltip() {
    if (_state != null) {
      _state!._showTooltip();
    }
  }

  void hideTooltip() {
    if (_state != null) {
      _state!._hideTooltip();
    }
  }

  /// 强制隐藏提示框，无论当前状态如何
  void forceHideTooltip() {
    if (_state != null) {
      _state!._forceHideTooltip();
    }
  }

  void toggleTooltip() {
    if (_state != null) {
      _state!._toggleTooltip();
    }
  }

  bool get isShowTooltip => _state?._isTooltipVisible ?? false;
}

/// 自定义提示框组件
///
/// 提供了丰富的自定义选项，包括提示框位置、样式、触发方式等。
/// 支持手动控制和鼠标悬停两种显示模式：
///
/// 1. 自动悬停模式（manual = false）：
///    - 当鼠标悬停在触发组件上时，经过 [waitDuration] 毫秒后显示提示框
///    - 鼠标离开触发组件时提示框会消失
///
/// 2. 手动控制模式（manual = true）：
///    - 提示框的显示和隐藏完全由控制器 [controller] 控制
///    - [waitDuration] 参数在此模式下不生效
class AppCustomTooltip extends StatefulWidget {
  final AppCustomTooltipController? controller;

  /// 提示框显示位置
  TooltipPlacement placement;

  /// 动画持续时长（毫秒）
  int animationDuration;

  /// 用户悬停等待时间（毫秒）
  ///
  /// 在非手动模式下（manual = false），用户鼠标悬停在触发组件上需要等待这个时间
  /// 才会显示提示框。这可以避免用户鼠标快速经过组件时的闪烁问题。
  ///
  /// 默认值为150毫秒。设置为0时将立即显示提示框。
  int waitDuration;

  /// 提示框显示动画类型
  TooltipAnimationType tooltipAnimationType;

  /// 显示框触发方式
  ///
  /// - false（默认值）：鼠标悬停触发，经过 [waitDuration] 时间后显示
  /// - true：手动控制显示，通过 [controller] 控制
  bool manual;

  /// 在手动模式下（manual = true），点击提示框和触发组件以外的区域时是否关闭提示框
  ///
  /// - false（默认值）：点击外部区域不会关闭提示框，完全由controller控制
  /// - true：点击外部区域会自动关闭提示框，并更新状态
  ///
  /// 在非手动模式下（manual = false），该属性不起作用
  bool closeOnOutsideClick;

  /// 提示框可见性变化的回调函数
  ///
  /// 当提示框显示或隐藏时会调用该回调，参数为当前的可见状态
  /// 在手动模式下点击外部区域关闭提示框时，会通过此回调通知父组件
  final ValueChanged<bool>? onVisibleChange;

  /// 提示框需要显示的内容
  String content;

  /// 提示框偏移量
  double offset;

  /// 主题模式 dark / light
  String effect;

  /// 是否显示箭头
  bool showArrow;

  /// 设置箭头颜色
  Color? arrowColor;

  /// 自定义提示内容
  Widget? contentChild;

  /// 触发组件
  Widget child;

  /// 创建自定义提示框
  ///
  /// [placement] 提示框位置，默认为底部左对齐
  /// [animationDuration] 动画持续时间（毫秒），默认300毫秒
  /// [waitDuration] 悬停等待时间（毫秒），默认150毫秒
  /// [tooltipAnimationType] 提示框显示动画类型，默认淡入动画
  /// [manual] 是否手动控制，默认false（悬停触发）
  /// [closeOnOutsideClick] 手动模式下点击外部是否关闭提示框，默认false
  /// [onVisibleChange] 提示框可见性变化的回调函数
  /// [content] 提示内容
  /// [offset] 提示框偏移距离，默认10像素
  /// [effect] 主题模式，默认'dark'
  /// [showArrow] 是否显示箭头，默认true
  /// [arrowColor] 箭头颜色，默认跟随主题
  /// [contentChild] 自定义提示内容组件
  /// [child] 触发组件
  AppCustomTooltip({
    super.key,
    this.controller,
    this.placement = TooltipPlacement.bottomLeft,
    this.animationDuration = 300,
    this.waitDuration = 150,
    this.tooltipAnimationType = TooltipAnimationType.fadeIn,
    this.manual = false,
    this.closeOnOutsideClick = false,
    this.onVisibleChange,
    this.content = '',
    this.offset = 10,
    this.effect = 'dark',
    this.showArrow = true,
    this.arrowColor,
    this.contentChild,
    required this.child,
  });

  @override
  State<AppCustomTooltip> createState() => _AppCustomTooltipState();
}

class _AppCustomTooltipState extends State<AppCustomTooltip> {
  // 新增：用于标记内容区域的 GlobalKey
  final GlobalKey _tooltipContentKey = GlobalKey();

  bool _lastIsShow = false;

  /// 水平箭头大小
  final Size _horizontalArrowSize = Size(10, 6);

  /// 垂直箭头大小
  final Size _verticalArrowSize = Size(6, 10);

  /// 用于延迟显示提示框的计时器
  Timer? _tooltipTimer;

  /// 触发显示提示框，根据模式处理waitDuration
  ///
  /// 在非手动模式下，会根据 waitDuration 参数延迟显示提示框
  /// 在手动模式下，直接显示提示框
  void _triggerShowTooltip() {
    // 手动模式下直接显示
    if (widget.manual) {
      _showTooltip();
      return;
    }

    // 非手动模式下，取消已有计时器
    _tooltipTimer?.cancel();

    // 设置计时器，延迟显示
    _tooltipTimer = Timer(Duration(milliseconds: widget.waitDuration), () {
      // 确保鼠标仍在触发组件上才显示提示框
      if (_isHoveringTrigger && mounted) {
        _showTooltip();
      }
    });
  }

  @override
  void initState() {
    super.initState();
    // 将自身注册到控制器
    widget.controller?._registerState(this);
  }

  // @override
  // void didUpdateWidget(PopupOverlay oldWidget) {
  //   super.didUpdateWidget(oldWidget);

  // }

  @override
  void didUpdateWidget(AppCustomTooltip oldWidget) {
    super.didUpdateWidget(oldWidget);

    if (widget.controller != oldWidget.controller) {
      oldWidget.controller?.dispose();
      widget.controller?._registerState(this);
    }
  }

  @override
  void dispose() {
    print('退出');

    // 取消定时器
    _tooltipTimer?.cancel();
    _tooltipTimer = null;

    // 直接移除悬浮提示框，不通过调用可能包含setState的方法
    if (_overlayEntry != null) {
      _overlayEntry!.remove();
      _overlayEntry = null;
      // 直接更新状态变量，不调用setState
      _isTooltipVisible = false;
      _lastIsShow = false;
    }

    // 组件销毁时通知控制器
    widget.controller?.dispose();

    super.dispose();
  }

  // 创建LayerLink实例，用于连接"目标Widget"和"悬浮Widget"
  final LayerLink _layerLink = LayerLink();

  // 用于跟踪悬浮提示框和菜单是否显示
  bool _isTooltipVisible = false;

  // 标记鼠标是否悬停在提示框上
  bool _isHoveringTooltip = false;

  // 标记鼠标是否悬停在触发组件上
  bool _isHoveringTrigger = false;

  // 是否正在过渡中（从触发组件移动到提示框或从提示框移动到触发组件）
  bool _isTransitioning = false;

  // 存储OverlayEntry实例
  OverlayEntry? _overlayEntry;

  // 切换悬浮提示框的显示状态
  void _toggleTooltip() {
    if (_isTooltipVisible) {
      _hideTooltip();
    } else {
      _triggerShowTooltip();
    }
  }

  /// 获取箭头颜色
  Color _getArrowColor() {
    if (widget.arrowColor != null) {
      return widget.arrowColor!;
    }
    return widget.effect == 'dark' ? Color(0xFF2C2C2C) : Color(0xFFF0F3FA);
  }

  double _getOffset() {
    double result = widget.offset;
    // 确保最小偏移量为8
    if (result < 8) result = 8;
    // 带箭头时保证最小偏移量为10
    if (widget.showArrow && result < 10) result = 10;
    return result;
  }

  // 隐藏悬浮提示框
  void _hideTooltip() {
    // 如果组件已被销毁，直接返回，避免后续操作
    if (!mounted) return;

    // 取消等待显示的计时器
    _tooltipTimer?.cancel();
    _tooltipTimer = null;

    // 在手动模式下也通过此方法隐藏提示框
    if (_overlayEntry != null) {
      // 只有在非手动模式或满足条件时才隐藏
      if (widget.manual ||
          (!_isHoveringTooltip && !_isHoveringTrigger && !_isTransitioning)) {
        _overlayEntry!.remove();
        _overlayEntry = null;

        if (mounted) {
          // 更新状态
          setState(() {
            _isTooltipVisible = false;
            _lastIsShow = false;
          });
        } else {
          _isTooltipVisible = false;
          _lastIsShow = false;
        }

        // 触发可见性变化回调
        if (widget.onVisibleChange != null) {
          widget.onVisibleChange!(false);
        }
      }
    }
  }

  // 创建并显示悬浮提示框
  void _showTooltip() {
    if (_overlayEntry != null || !mounted) {
      return;
    }

    try {
      final triggerRect = _getTooltipRect(context);

      // 创建OverlayEntry
      _overlayEntry = OverlayEntry(
        builder: (context) {
          return Stack(
            children: [
              // 添加一个透明层用于捕获事件
              if (!widget.manual ||
                  (widget.manual && widget.closeOnOutsideClick))
                Positioned.fill(
                  child: Listener(
                    behavior: HitTestBehavior.translucent,
                    onPointerMove: (event) {
                      // 非手动模式下的鼠标移动处理
                      if (!widget.manual) {
                        // 当鼠标在移动时保持提示框显示
                        _isTransitioning = true;

                        // 检查鼠标是否在触发组件上
                        if (triggerRect.contains(event.position)) {
                          if (!_isHoveringTrigger) {
                            if (mounted) {
                              setState(() {
                                _isHoveringTrigger = true;
                              });
                            } else {
                              _isHoveringTrigger = true;
                            }
                          }
                        } else {
                          if (_isHoveringTrigger) {
                            if (mounted) {
                              setState(() {
                                _isHoveringTrigger = false;
                              });
                            } else {
                              _isHoveringTrigger = false;
                            }
                          }
                        }
                      }
                    },
                    onPointerHover: (event) {
                      // 非手动模式下的鼠标悬停处理
                      if (!widget.manual) {
                        // 更新鼠标悬停状态
                        final RenderBox? tooltipBox = _findTooltipRenderBox();
                        if (tooltipBox != null) {
                          final tooltipRect =
                              tooltipBox.localToGlobal(Offset.zero) &
                              tooltipBox.size;
                          bool isInTooltip = tooltipRect.contains(
                            event.position,
                          );
                          bool isInTrigger = triggerRect.contains(
                            event.position,
                          );

                          // 更新悬停状态
                          if (isInTooltip && !_isHoveringTooltip) {
                            if (mounted) {
                              setState(() {
                                _isHoveringTooltip = true;
                                _isTransitioning = false;
                              });
                            } else {
                              _isHoveringTooltip = true;
                              _isTransitioning = false;
                            }
                          } else if (_isHoveringTooltip && !isInTooltip) {
                            if (mounted) {
                              setState(() {
                                _isHoveringTooltip = false;
                              });
                            } else {
                              _isHoveringTooltip = false;
                            }

                            if (isInTrigger) {
                              if (mounted) {
                                setState(() {
                                  _isHoveringTrigger = true;
                                  _isTransitioning = false;
                                });
                              } else {
                                _isHoveringTrigger = true;
                                _isTransitioning = false;
                              }
                            } else {
                              if (mounted) {
                                setState(() {
                                  _isHoveringTrigger = false;
                                });
                              } else {
                                _isHoveringTrigger = false;
                              }

                              // 如果既不在提示框上也不在触发器上，延迟隐藏
                              Future.delayed(Duration(milliseconds: 100), () {
                                if (!mounted) return; // 防止在组件销毁后执行
                                if (!_isHoveringTooltip &&
                                    !_isHoveringTrigger) {
                                  if (mounted) {
                                    setState(() {
                                      _isTransitioning = false;
                                    });
                                  } else {
                                    _isTransitioning = false;
                                  }
                                  _hideTooltip();
                                }
                              });
                            }
                          }
                        }
                      }
                    },
                    onPointerDown: (event) {
                      // 手动模式下的点击处理（点击外部关闭）
                      if (widget.manual && widget.closeOnOutsideClick) {
                        // 检查点击位置是否在提示框或触发组件区域内
                        final RenderBox? tooltipBox = _findTooltipRenderBox();
                        if (tooltipBox != null) {
                          final tooltipRect =
                              tooltipBox.localToGlobal(Offset.zero) &
                              tooltipBox.size;

                          // 判断点击是否在提示框或触发组件外部
                          bool isOutsideTooltip =
                              !tooltipRect.contains(event.position);
                          bool isOutsideTrigger =
                              !triggerRect.contains(event.position);

                          // 如果点击在两者之外，关闭提示框
                          if (isOutsideTooltip && isOutsideTrigger) {
                            // 通知父组件提示框已关闭
                            _notifyTooltipClosed();
                          }
                        }
                      }
                    },
                    child: Container(),
                  ),
                ),
              CompositedTransformFollower(
                link: _layerLink,
                showWhenUnlinked: false,
                targetAnchor: _getTargetAnchor(),
                followerAnchor: _getFollowerAnchor(),
                offset: _getDropdownOffset(),
                child: TweenAnimationBuilder<double>(
                  tween: Tween<double>(
                    begin: 0,
                    end: _isTooltipVisible ? 1.0 : 0.0,
                  ),
                  duration: Duration(milliseconds: widget.animationDuration),
                  curve: Curves.easeInOut,
                  builder: (context, value, child) {
                    // 弹框显示的内容
                    var displayContent = Stack(
                      clipBehavior: Clip.none, // 允许子组件超出Stack的范围
                      children: [
                        widget.contentChild != null
                            ? Container(
                              // key: ValueKey('tooltip_content_child'),
                              key: _tooltipContentKey, // 新增
                              child: widget.contentChild,
                            )
                            : Material(
                              elevation: 0.0,
                              borderRadius: BorderRadius.circular(4),
                              color: _getArrowColor(),
                              child: Container(
                                // key: ValueKey('tooltip_content'),
                                key: _tooltipContentKey, // 新增
                                padding: const EdgeInsets.symmetric(
                                  horizontal: 8,
                                  vertical: 5,
                                ),
                                decoration: BoxDecoration(),
                                child: Text(
                                  widget.content,

                                  style: TextStyle(
                                    color:
                                        widget.effect == 'dark'
                                            ? Color(0xFFFFFFFF)
                                            : Color(0xFF141414),
                                  ),
                                ),
                              ),
                            ),
                        // 添加箭头指示器
                        if (widget.showArrow) _buildArrowIndicator(),
                      ],
                    );

                    return MouseRegion(
                      onEnter: (_) {
                        if (!widget.manual) {
                          if (mounted) {
                            setState(() {
                              _isHoveringTooltip = true;
                              _isTransitioning = false;
                            });
                          } else {
                            _isHoveringTooltip = true;
                            _isTransitioning = false;
                          }
                        }
                      },
                      onExit: (_) {
                        if (!widget.manual) {
                          if (mounted) {
                            setState(() {
                              _isHoveringTooltip = false;
                            });
                          } else {
                            _isHoveringTooltip = false;
                          }

                          // 给一点延迟以避免闪烁
                          Future.delayed(Duration(milliseconds: 50), () {
                            if (!mounted) return; // 防止在组件销毁后执行
                            if (!_isHoveringTooltip &&
                                !_isHoveringTrigger &&
                                !_isTransitioning) {
                              _hideTooltip();
                            }
                          });
                        }
                      },
                      // 根据动画类型选择不同的动画效果
                      child:
                          widget.tooltipAnimationType ==
                                  TooltipAnimationType.fadeIn
                              ? Opacity(
                                opacity: value,
                                child: Row(
                                  mainAxisSize: MainAxisSize.min,
                                  children: [displayContent],
                                ),
                              )
                              : Transform.scale(
                                scale: value,
                                child: Opacity(
                                  opacity: value,
                                  child: Row(
                                    mainAxisSize: MainAxisSize.min,
                                    children: [displayContent],
                                  ),
                                ),
                              ),
                    );
                  },
                ),
              ),
            ],
          );
        },
      );

      // 将OverlayEntry添加到Overlay
      Overlay.of(context, rootOverlay: true).insert(_overlayEntry!);

      // 更新状态
      if (mounted) {
        setState(() {
          _isTooltipVisible = true;
          _lastIsShow = true;
        });
      } else {
        _isTooltipVisible = true;
        _lastIsShow = true;
      }

      // 通知父组件提示框已显示
      if (widget.onVisibleChange != null) {
        widget.onVisibleChange!(true);
      }
    } catch (e) {
      // 记录错误但不影响UI
    }
  }

  // 构建箭头指示器
  Widget _buildArrowIndicator() {
    final tooltipColor = _getArrowColor();

    switch (widget.placement) {
      case TooltipPlacement.topLeft:
      case TooltipPlacement.topCenter:
      case TooltipPlacement.topRight:
        // 箭头在顶部
        return Positioned(
          bottom: -6,
          left: _getArrowHorizontalPosition(),
          child: CustomPaint(
            size: _horizontalArrowSize,
            painter: ArrowPainter(
              direction: ArrowDirection.down,
              color: tooltipColor,
            ),
          ),
        );

      case TooltipPlacement.bottomLeft:
      case TooltipPlacement.bottomCenter:
      case TooltipPlacement.bottomRight:
        // 箭头在底部
        return Positioned(
          top: -6,
          left: _getArrowHorizontalPosition(),
          child: CustomPaint(
            size: _horizontalArrowSize,
            painter: ArrowPainter(
              direction: ArrowDirection.up,
              color: tooltipColor,
            ),
          ),
        );

      case TooltipPlacement.leftTop:
      case TooltipPlacement.leftCenter:
      case TooltipPlacement.leftBottom:
        // 箭头在左侧
        return Positioned(
          right: -6,
          top: _getArrowVerticalPosition(),
          child: CustomPaint(
            size: _verticalArrowSize,
            painter: ArrowPainter(
              direction: ArrowDirection.right,
              color: tooltipColor,
            ),
          ),
        );

      case TooltipPlacement.rightTop:
      case TooltipPlacement.rightCenter:
      case TooltipPlacement.rightBottom:
      default:
        // 箭头在右侧
        return Positioned(
          left: -6,
          top: _getArrowVerticalPosition(),
          child: CustomPaint(
            size: _verticalArrowSize,
            painter: ArrowPainter(
              direction: ArrowDirection.left,
              color: tooltipColor,
            ),
          ),
        );
    }
  }

  // 获取箭头水平位置
  double _getArrowHorizontalPosition() {
    final RenderBox? tooltipBox = _findTooltipRenderBox();

    if (tooltipBox == null) return 10;
    // 提示框size
    final tooltipRect = tooltipBox.localToGlobal(Offset.zero) & tooltipBox.size;

    // 触发组件size
    final triggerRect = _getTooltipRect(context);

    double result = 10.0;

    switch (widget.placement) {
      case TooltipPlacement.topLeft:
      case TooltipPlacement.bottomLeft:
        if (triggerRect.width < result * 2 + _horizontalArrowSize.width) {
          result = triggerRect.width / 2 - _horizontalArrowSize.width / 2;
        }
        break;
      case TooltipPlacement.topCenter:
      case TooltipPlacement.bottomCenter:
        result = tooltipRect.width / 2 - _horizontalArrowSize.width / 2;
        break;
      case TooltipPlacement.topRight:
      case TooltipPlacement.bottomRight:
        if (triggerRect.width < result * 2 + _horizontalArrowSize.width) {
          result =
              tooltipRect.width -
              _horizontalArrowSize.width -
              (triggerRect.width / 2 - _horizontalArrowSize.width / 2);
        } else {
          result = tooltipRect.width - 10 - _horizontalArrowSize.width;
        }
        break;
      default:
        break;
    }
    return result;
  }

  // 获取箭头垂直位置
  double _getArrowVerticalPosition() {
    final RenderBox? tooltipBox = _findTooltipRenderBox();
    if (tooltipBox == null) return 10;

    final tooltipRect = tooltipBox.localToGlobal(Offset.zero) & tooltipBox.size;

    switch (widget.placement) {
      case TooltipPlacement.leftTop:
      case TooltipPlacement.rightTop:
        return 10;
      case TooltipPlacement.leftCenter:
      case TooltipPlacement.rightCenter:
        return tooltipRect.height / 2 - _verticalArrowSize.height / 2;
      case TooltipPlacement.leftBottom:
      case TooltipPlacement.rightBottom:
        return tooltipRect.height - 10 - _verticalArrowSize.height;
      default:
        return 10;
    }
  }

  // 强制隐藏提示框，用于手动模式
  void _forceHideTooltip() {
    // 如果组件已销毁，直接返回
    if (!mounted) return;

    if (_overlayEntry != null) {
      _overlayEntry!.remove();
      _overlayEntry = null;
      if (mounted) {
        setState(() {
          _isTooltipVisible = false;
          _lastIsShow = false;
        });
      } else {
        _isTooltipVisible = false;
        _lastIsShow = false;
      }

      // 触发可见性变化回调
      if (widget.onVisibleChange != null) {
        widget.onVisibleChange!(false);
      }
    }
  }

  // 通知父组件提示框已关闭（用于点击外部区域关闭时）
  void _notifyTooltipClosed() {
    // 如果组件已销毁，直接返回
    if (!mounted) return;

    // 先隐藏提示框
    if (_overlayEntry != null) {
      _overlayEntry!.remove();
      _overlayEntry = null;

      if (mounted) {
        setState(() {
          _isTooltipVisible = false;
          _lastIsShow = false;
        });
      } else {
        _isTooltipVisible = false;
        _lastIsShow = false;
      }

      // 通过回调函数通知父组件状态变化
      if (widget.onVisibleChange != null) {
        widget.onVisibleChange!(false);
      }
    }
  }

  /// 获取触发组件size
  Rect _getTooltipRect(BuildContext context) {
    final RenderBox renderBox = context.findRenderObject() as RenderBox;
    //触发组件的size
    final size = renderBox.size;
    // 触发组件的全局位置
    final position = renderBox.localToGlobal(Offset.zero);
    // 触发组件的矩形区域
    final triggerRect = position & size;

    return triggerRect;
  }

  // 查找提示框的RenderBox
  RenderBox? _findTooltipRenderBox() {
    final currentContext = _tooltipContentKey.currentContext;
    if (currentContext == null) return null;
    return currentContext.findRenderObject() as RenderBox?;

    // if (WidgetsBinding.instance.renderViewElement == null) return null;

    // Element? result;
    // void visitor(Element element) {
    //   // 检查是否有 tooltip_content key
    //   if (element.widget is Container &&
    //       (element.widget as Container).key == ValueKey('tooltip_content')) {
    //     result = element;
    //     return;
    //   }

    //   // 检查是否有 tooltip_content_child key（用于 contentChild 情况）
    //   if (element.widget is Container &&
    //       (element.widget as Container).key ==
    //           ValueKey('tooltip_content_child')) {
    //     result = element;
    //     return;
    //   }

    //   if (result == null) {
    //     element.visitChildren(visitor);
    //   }
    // }

    // // 遍历Element树
    // WidgetsBinding.instance.renderViewElement!.visitChildren(visitor);

    // // 如果找到了元素，返回其RenderBox
    // return result?.findRenderObject() as RenderBox?;
  }

  Alignment _getFollowerAnchor() {
    switch (widget.placement) {
      case TooltipPlacement.topLeft:
        return Alignment.bottomLeft;
      case TooltipPlacement.topCenter:
        return Alignment.bottomCenter;
      case TooltipPlacement.topRight:
        return Alignment.bottomRight;
      case TooltipPlacement.rightTop:
        return Alignment.topLeft;
      case TooltipPlacement.rightCenter:
        return Alignment.centerLeft;
      case TooltipPlacement.rightBottom:
        return Alignment.bottomLeft;
      case TooltipPlacement.bottomLeft:
        return Alignment.topLeft;
      case TooltipPlacement.bottomCenter:
        return Alignment.topCenter;
      case TooltipPlacement.bottomRight:
        return Alignment.topRight;
      case TooltipPlacement.leftTop:
        return Alignment.topRight;
      case TooltipPlacement.leftCenter:
        return Alignment.centerRight;
      case TooltipPlacement.leftBottom:
        return Alignment.bottomRight;
    }
  }

  Alignment _getTargetAnchor() {
    switch (widget.placement) {
      case TooltipPlacement.topLeft:
        return Alignment.topLeft;
      case TooltipPlacement.topCenter:
        return Alignment.topCenter;
      case TooltipPlacement.topRight:
        return Alignment.topRight;
      case TooltipPlacement.rightTop:
        return Alignment.topRight;
      case TooltipPlacement.rightCenter:
        return Alignment.centerRight;
      case TooltipPlacement.rightBottom:
        return Alignment.bottomRight;
      case TooltipPlacement.bottomLeft:
        return Alignment.bottomLeft;
      case TooltipPlacement.bottomCenter:
        return Alignment.bottomCenter;
      case TooltipPlacement.bottomRight:
        return Alignment.bottomRight;
      case TooltipPlacement.leftTop:
        return Alignment.topLeft;
      case TooltipPlacement.leftCenter:
        return Alignment.centerLeft;
      case TooltipPlacement.leftBottom:
        return Alignment.bottomLeft;
    }
  }

  Offset _getDropdownOffset() {
    double offset = _getOffset();
    switch (widget.placement) {
      case TooltipPlacement.topLeft:
      case TooltipPlacement.topCenter:
      case TooltipPlacement.topRight:
        return Offset(0, -offset);
      case TooltipPlacement.rightTop:
      case TooltipPlacement.rightCenter:
      case TooltipPlacement.rightBottom:
        return Offset(offset, 0);
      case TooltipPlacement.bottomLeft:
      case TooltipPlacement.bottomCenter:
      case TooltipPlacement.bottomRight:
        return Offset(0, offset);
      case TooltipPlacement.leftTop:
      case TooltipPlacement.leftCenter:
      case TooltipPlacement.leftBottom:
        return Offset(-offset, 0);
    }
  }

  @override
  Widget build(BuildContext context) {
    return CompositedTransformTarget(
      link: _layerLink,
      child: MouseRegion(
        onEnter: (_) {
          if (!widget.manual) {
            if (mounted) {
              setState(() {
                _isHoveringTrigger = true;
              });
            } else {
              _isHoveringTrigger = true;
            }
            _triggerShowTooltip();
          }
        },
        onExit: (_) {
          if (!widget.manual) {
            // 取消等待显示的计时器

            _tooltipTimer?.cancel();
            _tooltipTimer = null;

            // 鼠标离开触发组件时
            if (mounted) {
              setState(() {
                _isHoveringTrigger = false;
                _isTransitioning = true;
              });
            } else {
              _isHoveringTrigger = false;
              _isTransitioning = true;
            }

            // 给一点时间，以便鼠标能够移动到提示框
            Future.delayed(Duration(milliseconds: 150), () {
              if (!mounted) return; // 防止在组件销毁后执行
              if (!_isHoveringTrigger && !_isHoveringTooltip) {
                if (mounted) {
                  setState(() {
                    _isTransitioning = false;
                  });
                } else {
                  _isTransitioning = false;
                }
                _hideTooltip();
              }
            });
          }
        },
        child: widget.child,
      ),
    );
  }
}

// 箭头方向枚举
enum ArrowDirection { up, down, left, right }

// 自定义箭头绘制
class ArrowPainter extends CustomPainter {
  final ArrowDirection direction;
  final Color color;

  ArrowPainter({required this.direction, required this.color});

  @override
  void paint(Canvas canvas, Size size) {
    final paint =
        Paint()
          ..color = color
          ..style = PaintingStyle.fill;

    final path = Path();

    switch (direction) {
      case ArrowDirection.up:
        path.moveTo(0, size.height);
        path.lineTo(size.width / 2, 0);
        path.lineTo(size.width, size.height);
        path.close();
        break;
      case ArrowDirection.down:
        path.moveTo(0, 0);
        path.lineTo(size.width, 0);
        path.lineTo(size.width / 2, size.height);
        path.close();
        break;
      case ArrowDirection.left:
        path.moveTo(size.width, 0);
        path.lineTo(size.width, size.height);
        path.lineTo(0, size.height / 2);
        path.close();
        break;
      case ArrowDirection.right:
        path.moveTo(0, 0);
        path.lineTo(0, size.height);
        path.lineTo(size.width, size.height / 2);
        path.close();
        break;
    }

    canvas.drawPath(path, paint);
  }

  @override
  bool shouldRepaint(covariant CustomPainter oldDelegate) {
    return oldDelegate is ArrowPainter &&
        (oldDelegate.direction != direction || oldDelegate.color != color);
  }
}
