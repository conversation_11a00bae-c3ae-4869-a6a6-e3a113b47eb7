import 'package:flutter/material.dart';
import 'package:octasync_client/api/positions.dart';
import 'package:octasync_client/imports.dart';
import 'package:octasync_client/models/pages_model/pages_model.dart';
import 'package:octasync_client/views/admin/organization/position_grade/grade_dialog.dart';
import 'package:octasync_client/views/admin/organization/position_grade/model/grade_model.dart';

import 'package:octasync_client/components/data/app_table/app_basic_text_field.dart';
import 'package:octasync_client/components/data/app_table/app_table.dart';
import 'package:octasync_client/components/data/app_table/app_table_column_type.dart';
import 'package:octasync_client/components/data/app_table/enums/table_checked_enum.dart';
import 'package:octasync_client/components/data/app_table/helper/app_table_general_helper.dart';
import 'package:octasync_client/components/data/app_table/model/app_table_column.dart';
import 'package:octasync_client/components/data/app_table/model/role_mgmt/employee.dart';
import 'package:octasync_client/components/data/app_table/model/role_mgmt/group_config.dart';
import 'package:octasync_client/components/data/app_table/model/role_mgmt/role.dart';
import 'package:octasync_client/components/data/app_table/state/app_table_state_manage.dart';

/// 职级页面
class GradePage extends StatefulWidget {
  const GradePage({super.key});

  @override
  State<GradePage> createState() => _GradePageState();
}

class _GradePageState extends State<GradePage> {
  // 创建 GlobalKey 用于访问 DepartmentSelector 的方法
  final GlobalKey<GradeDialogState> _gradeDialogStateKey = GlobalKey<GradeDialogState>();
  late AppTableStateManage _stateManage;

  // 将列定义移到方法中，避免在初始化器中访问实例成员
  List<AppTableColumn> get colsTemp => [
    AppTableColumn(
      text: '名称',
      field: 'Name',
      type: AppTableColumnType.text(),
      width: 100 + 200,
      resizable: true, // 允许调整列宽
      frozen: AppTableColumnFrozen.start,
    ),
    AppTableColumn(
      text: '职级序列',
      field: 'LevelSequenceenum',
      type: AppTableColumnType.text(),
      frozen: AppTableColumnFrozen.start,
      width: 150,
      resizable: true, // 允许调整列宽
    ),
    AppTableColumn(
      text: '操作',
      field: 'Opt',
      type: AppTableColumnType.text(),
      width: 80,
      resizable: false, // 允许调整列宽
      showMore: false,
      cellBuilder: (context, value, column, row) {
        return AppDropdown(
          items: [
            DropdownItem(text: '编辑', value: 'edit'),
            DropdownItem(text: '删除', value: 'delete'),
          ],
          trigger: DropdownTrigger.click,
          onItemSelected: (item) {
            switch (item.value) {
              case 'edit':
                print('点击编辑');
                break;
              case 'delete':
                // 从行数据中获取ID并传递给deleteItem方法
                final id = row['Id']?.toString();
                deleteItem(id!);
                break;
              default:
            }
          },
          child: Icon(IconFont.xianxing_gengduo),
        );
      },
    ),
  ];

  @override
  void initState() {
    super.initState();
  }

  /// 获取列表
  Future<void> getList() async {
    try {
      _stateManage.setLoading(true);
      final response = await PositionsApi.getListPage({});

      List<dynamic> listJson = response['Items'];
      List<Map<String, dynamic>> jsonRowsStr =
          listJson.map((r) => r as Map<String, dynamic>).toList();
      var rows = _stateManage.convertToTreeData(jsonRowsStr);

      _stateManage.setRows(rows);
    } finally {
      _stateManage.setLoading(false);
    }
  }

  /// 删除
  Future<void> deleteItem(String id) async {
    try {
      _stateManage.setLoading(true);
      await PositionsApi.delete([id]);
      getList();
    } finally {
      _stateManage.setLoading(false);
    }
  }

  @override
  Widget build(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        GradeDialog(
          key: _gradeDialogStateKey,
          onSuccess: () => getList(),
          child: AppButton(
            text: '添加职级',
            type: ButtonType.primary,
            onPressed: () {
              _gradeDialogStateKey.currentState?.showGradeDialog(context);
            },
          ),
        ),
        SizedBox(height: 10),
        Expanded(
          child: AppTable(
            uniqueId: 'Id',
            showAddRowButton: false,
            showAddColumnButton: false,
            onLoaded: (stateManage) {
              _stateManage = stateManage;
              _stateManage.setColumns(colsTemp);
              getList();
            },
            onLoadMore: () {
              // loadMore();
            },
          ),
        ),
      ],
    );
  }
}
