import 'package:flutter/material.dart';
import 'package:octasync_client/components/data/app_table/app_table_column_type.dart';
import 'package:octasync_client/components/data/app_table/enums/column_type_enum.dart';
import 'package:octasync_client/components/data/app_table/model/column_types/app_table_column_type_checkbox.dart';
import 'package:octasync_client/components/data/app_table/model/column_types/app_table_column_type_currency.dart';
import 'package:octasync_client/components/data/app_table/model/column_types/app_table_column_type_date.dart';
import 'package:octasync_client/components/data/app_table/model/column_types/app_table_column_type_hyperlink.dart';
import 'package:octasync_client/components/data/app_table/model/column_types/app_table_column_type_number.dart';
import 'package:octasync_client/components/data/app_table/model/column_types/app_table_column_type_parent_record.dart';
import 'package:octasync_client/components/data/app_table/model/column_types/app_table_column_type_percentage.dart';
import 'package:octasync_client/components/data/app_table/model/column_types/app_table_column_type_single_select.dart';
import 'package:octasync_client/components/data/app_table/model/column_types/app_table_column_type_telephone.dart';
import 'package:octasync_client/components/data/app_table/model/column_types/app_table_column_type_text.dart';

import 'package:json_annotation/json_annotation.dart';

part 'app_table_column.g.dart';

@JsonSerializable()
class AppTableColumn {
  String field;
  String text;
  ColumnTypeEnum typeCode;

  @JsonKey(fromJson: _typeFromJson, toJson: _typeToJson)
  AppTableColumnType type;

  @JsonKey(ignore: true)
  final AlignmentGeometry? alignment;

  double width;
  final double minWidth;

  /// 更多操作（列上的三个点按钮——下拉）
  bool showMore;

  @JsonKey(ignore: true)
  final Widget Function(
    BuildContext context,
    dynamic value,
    AppTableColumn column,
    Map<String, dynamic> row,
  )?
  cellBuilder; // 自定义单元格构建器

  @JsonKey(ignore: true)
  final Widget Function(BuildContext context)? headerBuilder; // 自定义表头构建器

  final bool sortable; // 是否可排序
  final bool resizable; // 是否可调整宽度

  AppTableColumnFrozen frozen;

  AppTableColumn({
    required this.field,
    required this.text,
    this.typeCode = ColumnTypeEnum.text,
    required this.type,
    this.alignment = Alignment.centerLeft,
    this.width = 200,
    this.minWidth = 100,
    this.showMore = true,

    this.cellBuilder,
    this.headerBuilder,
    this.sortable = false,
    this.resizable = false,
    this.frozen = AppTableColumnFrozen.none,
  });

  AppTableColumn clone() => AppTableColumn(
    field: field,
    text: text,
    type: type,
    alignment: alignment,
    width: width,
    minWidth: minWidth,
    showMore: showMore,
    cellBuilder: cellBuilder,
    headerBuilder: headerBuilder,
    sortable: sortable,
    resizable: resizable,
    frozen: frozen,
  );

  @override
  // TODO: implement hashCode
  int get hashCode => Object.hash(
    field,
    text,
    type,
    alignment,
    width,
    minWidth,
    showMore,
    cellBuilder,
    headerBuilder,
    sortable,
    resizable,
    frozen,
  );

  @override
  bool operator ==(Object other) {
    // TODO: implement ==
    return identical(this, other) ||
        (other is AppTableColumn &&
            other.field == field &&
            other.text == text &&
            other.type == type &&
            other.alignment == alignment &&
            other.width == width &&
            other.minWidth == minWidth &&
            other.showMore == showMore &&
            other.cellBuilder == cellBuilder &&
            other.headerBuilder == headerBuilder &&
            other.sortable == sortable &&
            other.resizable == resizable &&
            other.frozen == frozen);
  }

  // 自定义序列化方法
  static AppTableColumnType _typeFromJson(Map<String, dynamic> json) {
    // 根据 typeCode 创建对应的类型实例
    final typeCode = json['typeCode'] as String?;

    switch (typeCode) {
      case 'text':
        return AppTableColumnTypeText.fromJson(json);
      case 'number':
        return AppTableColumnTypeNumber.fromJson(json);
      case 'singleSelect':
      case 'multipleSelect':
        return AppTableColumnTypeSingleSelect.fromJson(json);
      case 'date':
        return AppTableColumnTypeDate.fromJson(json);
      case 'currency':
        return AppTableColumnTypeCurrency.fromJson(json);
      case 'percentage':
        return AppTableColumnTypePercentage.fromJson(json);
      case 'telephone':
        return AppTableColumnTypeTelephone.fromJson(json);
      case 'checkbox':
        return AppTableColumnTypeCheckbox.fromJson(json);
      case 'hyperlink':
        return AppTableColumnTypeHyperlink.fromJson(json);
      case 'parentRecord':
        return AppTableColumnTypeParentRecord.fromJson(json);
      default:
        // 默认返回 text 类型
        return AppTableColumnTypeText.fromJson(json);
    }
  }

  static Map<String, dynamic> _typeToJson(AppTableColumnType type) {
    return type.toJson();
  }

  factory AppTableColumn.fromJson(Map<String, dynamic> json) {
    // 保留原始json用于后续处理
    final originalJson = Map<String, dynamic>.from(json);

    return _$AppTableColumnFromJson(originalJson);
  }

  Map<String, dynamic> toJson() => _$AppTableColumnToJson(this);
}

// class Pointer {
//   double x;
//   double width;
//   Pointer({required this.x, required this.width});

//   List<Pointer> list = [Pointer(x: 10, width: 100), Pointer(x: 10, width: 100)];
// }

enum AppTableColumnFrozen { none, start }
