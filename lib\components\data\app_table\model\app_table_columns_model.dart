import 'package:octasync_client/components/data/app_table/model/app_table_column.dart';
import 'package:json_annotation/json_annotation.dart';

part 'app_table_columns_model.g.dart';

@JsonSerializable()
class AppTableColumnsModel {
  String smartTableHeaderId;
  List<AppTableColumn> headerSet;

  AppTableColumnsModel({required this.smartTableHeaderId, required this.headerSet});

  factory AppTableColumnsModel.fromJson(Map<String, dynamic> json) =>
      _$AppTableColumnsModelFromJson(json);
  @override
  Map<String, dynamic> toJson() => _$AppTableColumnsModelToJson(this);
}
