import 'package:flutter/material.dart';
import 'package:octasync_client/api/positions.dart';
import 'package:octasync_client/imports.dart';
import 'package:octasync_client/views/admin/organization/position_grade/enum.dart';

/// 弹窗类型
enum DialogTypeEmun { create, edit }

/// 职级创建弹窗
class GradeDialog extends StatefulWidget {
  final Widget? child;
  final void Function()? onSuccess; // 提交成功回调
  const GradeDialog({super.key, this.child, this.onSuccess});

  @override
  State<GradeDialog> createState() => GradeDialogState();
}

// 将 State 类改为公开，以便外部可以访问
class GradeDialogState extends State<GradeDialog> {
  /// 是否继续添加下一条
  bool isAddNext = false;
  bool btnLoading = false;

  final GlobalKey<FormState> _formKey = GlobalKey<FormState>();
  final TextEditingController _nameController = TextEditingController();
  final SelectController<int> _gradeSelectController = SelectController<int>();

  @override
  void initState() {
    super.initState();
  }

  /// 重置数据
  void resetFormData([StateSetter? setDialogState]) {
    _nameController.text = '';
    _gradeSelectController.clear();
  }

  /// 提交
  createRequest(BuildContext context, [StateSetter? setDialogState]) async {
    // 使用 Form 的校验功能
    if (!_formKey.currentState!.validate()) {
      ToastManager.error('请填写完整信息');
      return;
    }
    setState(() {
      btnLoading = true;
    });
    final params = {
      'Name': _nameController.text,
      'LevelSequenceenum': _gradeSelectController.value,
    };

    try {
      await PositionsApi.add(params);
      setState(() {
        btnLoading = false;
      });
      resetFormData();

      ToastManager.success('提交成功');
      widget.onSuccess?.call();
      if (mounted && !isAddNext) {
        context.pop();
      }
    } catch (err) {
      ToastManager.error('提交失败');
      setState(() {
        btnLoading = false;
      });
    }
  }

  /// 打开添加部门弹窗
  void showGradeDialog(BuildContext context) {
    double labelWidth = 80;

    Widget buildNameInput() {
      return AppInput(
        label: "名称",
        required: true,
        labelWidth: labelWidth,
        labelPosition: LabelPosition.left,
        hintText: "名称",
        size: InputSize.medium,
        controller: _nameController,
        maxLength: 30,
        validator: (value) {
          if (value == null || value.isEmpty) {
            return '请输入名称';
          }
          return null;
        },
        onChanged: (value) {},
      );
    }

    Widget buildPositionSelect() {
      return Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          SizedBox(
            width: labelWidth,
            child: Row(
              children: [
                Text('职级序列'),
                Text(' *', style: TextStyle(fontSize: 20, color: AppColors.error)),
              ],
            ),
          ),
          Expanded(
            child: FormField<int>(
              validator: (value) {
                if (_gradeSelectController.value == null) {
                  return '请选择职级序列';
                }
                return null;
              },
              builder: (FormFieldState<int> field) {
                return Column(
                  children: [
                    AppSelect<int>(
                      placeholder: '请选择',
                      options: PositionGradeEnum.positionOptions,
                      controller: _gradeSelectController,
                      onChanged: (value) {
                        field.didChange(value);
                      },
                    ),

                    SizedBox(
                      height: 18,
                      child: Align(
                        alignment: Alignment.topLeft,
                        child: AnimatedOpacity(
                          duration: const Duration(milliseconds: 200),
                          opacity: field.hasError ? 1.0 : 0.0,
                          child: Padding(
                            padding: const EdgeInsets.only(top: 4.0),
                            child: Text(
                              field.hasError ? (field.errorText ?? '') : '',
                              style: TextStyle(fontSize: 11, color: AppColors.error, height: 1.2),
                              maxLines: 1,
                              overflow: TextOverflow.ellipsis,
                            ),
                          ),
                        ),
                      ),
                    ),
                  ],
                );
              },
            ),
          ),
        ],
      );
    }

    AppDialog.show(
      width: 480,
      context: context,
      title: '添加职级',
      isDrawer: true,
      slideDirection: SlideDirection.right,
      showFooter: false,
      barrierDismissible: true,
      padding: EdgeInsetsGeometry.zero,
      child: StatefulBuilder(
        builder: (context, setDialogState) {
          return Column(
            children: [
              Expanded(
                child: Padding(
                  padding: const EdgeInsets.all(10),
                  child: Form(
                    key: _formKey,
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      spacing: 10,
                      children: [buildNameInput(), buildPositionSelect()],
                    ),
                  ),
                ),
              ),
              Divider(),
              Padding(
                padding: const EdgeInsets.all(10),
                child: Row(
                  mainAxisAlignment: MainAxisAlignment.end,
                  children: [
                    Checkbox(
                      value: isAddNext,
                      onChanged: (value) {
                        setDialogState(() {
                          isAddNext = !isAddNext;
                        });
                      },
                    ),
                    Text('继续新建下一条'),
                    const SizedBox(width: 10),
                    AppButton(
                      text: '取消',
                      type: ButtonType.default_,
                      onPressed: () => context.pop(),
                    ),
                    const SizedBox(width: 10),
                    AppButton(
                      text: '确定',
                      type: ButtonType.primary,
                      onPressed: () => createRequest(context, setDialogState),
                    ),
                  ],
                ),
              ),
            ],
          );
        },
      ),
    );
  }

  @override
  void dispose() {
    _nameController.dispose();
    _gradeSelectController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return widget.child ?? SizedBox();
  }
}
