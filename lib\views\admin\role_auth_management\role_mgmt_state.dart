import 'package:flutter/cupertino.dart';
import 'package:octasync_client/components/data/app_table/model/role_mgmt/group_config.dart';
import 'package:octasync_client/components/data/app_table/model/role_mgmt/role.dart';

class RoleMgmtState with ChangeNotifier {
  ///——————————————————————————————————————————————角色分组相关
  /// 分组弹框是否显示
  bool _showCreateRoleGroupDialog = false;
  String _dialogStatus = 'create';
  GroupConfig _groupConfigModel = GroupConfig();
  int _reloadGroupListFlag = 0;

  bool get showCreateRoleGroupDialog => _showCreateRoleGroupDialog;
  GroupConfig get groupConfigModel => _groupConfigModel;
  String get dialogStatus => _dialogStatus;
  int get reloadGroupListFlag => _reloadGroupListFlag;

  /// 创建分组
  void handleGroupCreate() {
    _dialogStatus = 'create';
    _showCreateRoleGroupDialog = true;
    _groupConfigModel = GroupConfig();
    _groupConfigModel.id = '00000000-0000-0000-0000-000000000000';
    notifyListeners();
  }

  /// 关闭创建分组弹框
  void closeGroupDialog() {
    _dialogStatus = '';
    _showCreateRoleGroupDialog = false;
    notifyListeners();
  }

  /// 刷新分组
  void reloadGroupList() {
    _reloadGroupListFlag++;
    notifyListeners();
  }

  ///——————————————————————————————————————————————角色相关
  bool _showCreateRoleDialog = false;
  String _dialogRoleStatus = 'create';
  Role _roleModel = new Role();

  bool get showCreateRoleDialog => _showCreateRoleDialog;
  String get dialogRoleStatus => _dialogRoleStatus;
  Role get roleModel => _roleModel;

  /// 创建分组
  void handleRoleCreate() {
    _dialogRoleStatus = 'create';
    _showCreateRoleDialog = true;
    _roleModel = Role();
    _roleModel.id = '00000000-0000-0000-0000-000000000000';
    _roleModel.groupId = '0198ab66-049c-7757-8ff7-2c625a3a1193';
    notifyListeners();
  }

  /// 关闭创建分组弹框
  void closeRoleDialog() {
    _dialogRoleStatus = '';
    _showCreateRoleDialog = false;
    notifyListeners();
  }
}
