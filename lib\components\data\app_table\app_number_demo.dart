import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:octasync_client/components/data/app_table/app_number.dart';
import 'package:octasync_client/components/data/app_table/app_select.dart';
import 'package:octasync_client/components/data/app_table/model/general_option.dart';

class AppNumberDemo extends StatefulWidget {
  const AppNumberDemo({super.key});

  @override
  State<AppNumberDemo> createState() => _AppNumberDemoState();
}

class _AppNumberDemoState extends State<AppNumberDemo> {
  /// 是否显示千分位
  bool _isShowPerc_1 = true;

  /// 是否补0
  bool _isRetainDec_1 = true;
  double? value1 = 1234.50;

  /// 是否显示千分位
  bool _isShowPerc_2 = true;

  /// 是否补0
  bool _isRetainDec_2 = true;
  double? value2 = 1234.50;

  int? value4 = 9999;
  double? value3 = -1234.56;

  int? value5;

  int selected1 = 1;
  List<GeneralOption<int>> option1 = [
    GeneralOption(id: 1, text: 'Option 1'),
    GeneralOption(id: 2, text: 'Option 2'),
    GeneralOption(id: 3, text: 'Option 3'),
  ];

  @override
  Widget build(BuildContext context) {
    return Container(
      child: Row(
        children: [
          Container(
            width: 200,
            child: Column(
              children: [
                Container(child: Text('两位小数：${value1}')),
                Row(
                  children: [
                    Checkbox(
                      value: _isShowPerc_1,
                      onChanged: (value) {
                        setState(() {
                          _isShowPerc_1 = value!;
                        });
                      },
                    ),
                    Text('是否显示千分位'),
                  ],
                ),
                Row(
                  children: [
                    Checkbox(
                      value: _isRetainDec_1,
                      onChanged: (value) {
                        setState(() {
                          _isRetainDec_1 = value!;
                        });
                      },
                    ),
                    Text('不足补0'),
                  ],
                ),
                AppNumber(
                  initialValue: value1,
                  precision: 2,
                  isShowPercentiles: _isShowPerc_1,
                  isRetainDecimal: _isRetainDec_1,
                  onChanged: (value) {
                    setState(() {
                      value1 = value;
                    });
                  },
                ),
              ],
            ),
          ),
          Container(
            width: 200,
            child: Column(
              children: [
                Container(child: Text('小数：${value2}')),
                Row(
                  children: [
                    Checkbox(
                      value: _isShowPerc_2,
                      onChanged: (value) {
                        setState(() {
                          _isShowPerc_2 = value!;
                        });
                      },
                    ),
                    Text('是否显示千分位'),
                  ],
                ),
                Row(
                  children: [
                    Checkbox(
                      value: _isRetainDec_2,
                      onChanged: (value) {
                        setState(() {
                          _isRetainDec_2 = value!;
                        });
                      },
                    ),
                    Text('不足补0'),
                  ],
                ),
                AppNumber(
                  initialValue: value2,
                  precision: 2,
                  isShowPercentiles: _isShowPerc_2,
                  isRetainDecimal: _isRetainDec_2,
                  onChanged: (value) {
                    setState(() {
                      value2 = value;
                    });
                  },
                ),
              ],
            ),
          ),

          Container(
            width: 200,
            child: Column(
              children: [
                Container(child: Text('整数：${value4}')),
                AppNumber(
                  initialValue: value4,
                  precision: 0,
                  onChanged: (value) {
                    setState(() {
                      value4 = value;
                    });
                  },
                ),
              ],
            ),
          ),

          Container(
            width: 200,
            child: Column(
              children: [
                Container(child: Text('负数：${value3}')),
                AppNumber(
                  initialValue: value3,
                  precision: 2,
                  negative: true,
                  onChanged: (value) {
                    setState(() {
                      value3 = value;
                    });
                  },
                ),
              ],
            ),
          ),

          Container(
            width: 200,
            child: Column(
              children: [
                Container(child: Text('支持null值：${value5}')),
                AppNumber(
                  initialValue: value5,
                  // precision: 2,
                  negative: false,
                  onChanged: (value) {
                    setState(() {
                      value5 = int.tryParse(value.toString());
                    });
                  },
                ),
              ],
            ),
          ),

          Container(
            width: 200,
            child: Column(
              children: [
                Container(child: Text('用于对比两个组件高度')),
                AppSelect<int>(
                  value: selected1,
                  options: option1,
                  onChanged: (item) {
                    setState(() {
                      selected1 = item;
                    });
                  },
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }
}
