import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:octasync_client/components/data/app_table/app_date.dart';
import 'package:octasync_client/components/data/app_table/app_number.dart';

class AppDateDemo extends StatefulWidget {
  const AppDateDemo({super.key});

  @override
  State<AppDateDemo> createState() => _AppDateDemoState();
}

String format1 = 'yyyy-MM-dd HH:mm:ss';
DateTime? date1 = DateTime(2025, 05, 24, 11, 30, 30);

String format2 = 'yyyy-MM-dd HH:mm';
DateTime? date2 = DateTime(2025, 05, 24, 11, 30);

String format3 = 'HH:mm:ss';
DateTime? date3 = DateTime(2025, 05, 24, 11, 30, 30);

String format4 = 'HH:mm';
DateTime? date4 = DateTime(2025, 05, 24, 11, 30);

class _AppDateDemoState extends State<AppDateDemo> {
  @override
  Widget build(BuildContext context) {
    return Container(
      child: Row(
        children: [
          _buildDemo1(),
          _buildDemo2(),
          // _buildDemo3(),
          //, _buildDemo4()
        ],
      ),
    );
  }

  Widget _buildDemo1() {
    return Container(
      width: 200,
      child: Column(
        children: [
          Container(child: Text('时间：${date1}')),
          Container(child: Text('调整格式：${format1}')),
          Column(
            children: [
              RadioListTile<String>(
                title: Text('yyyy-MM-dd HH:mm:ss'),
                value: 'yyyy-MM-dd HH:mm:ss',
                groupValue: format1,
                onChanged: (value) {
                  setState(() {
                    format1 = value!;
                  });
                },
              ),
              RadioListTile<String>(
                title: Text('yyyy-MM-dd'),
                value: 'yyyy-MM-dd',
                groupValue: format1,
                onChanged: (value) {
                  setState(() {
                    format1 = value!;
                  });
                },
              ),
            ],
          ),

          AppDate(
            initialValue: date1,
            format: format1,
            onChanged: (value) {
              setState(() {
                date1 = value;
              });
            },
          ),
        ],
      ),
    );
  }

  Widget _buildDemo2() {
    return Container(
      width: 200,
      child: Column(
        children: [
          Container(child: Text('时间：${date2}')),
          AppDate(
            initialValue: date2,
            format: format2,
            onChanged: (value) {
              setState(() {
                date2 = value;
              });
            },
          ),
        ],
      ),
    );
  }

  Widget _buildDemo3() {
    return Container(
      width: 200,
      child: Column(
        children: [
          Container(child: Text('时间：${date3}')),
          AppDate(
            initialValue: date3,
            format: format3,
            onChanged: (value) {
              setState(() {
                date3 = value;
              });
            },
          ),
        ],
      ),
    );
  }

  Widget _buildDemo4() {
    return Container(
      width: 200,
      child: Column(
        children: [
          Container(child: Text('时间：${date4}')),
          AppDate(
            initialValue: date4,
            format: format4,
            onChanged: (value) {
              setState(() {
                date4 = value;
              });
            },
          ),
        ],
      ),
    );
  }
}
