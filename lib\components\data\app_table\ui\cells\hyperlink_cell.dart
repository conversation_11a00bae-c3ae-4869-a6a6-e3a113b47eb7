import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:octasync_client/components/data/app_table/app_hyperlink.dart';
import 'package:octasync_client/components/data/app_table/enums/cell_state_enum.dart';
import 'package:octasync_client/components/data/app_table/model/app_table_column.dart';
import 'package:octasync_client/components/data/app_table/model/column_types/app_table_column_type_hyperlink.dart';
import 'package:url_launcher/url_launcher.dart';

class HyperlinkCell extends StatefulWidget {
  const HyperlinkCell({
    super.key,
    this.state = CellStateEnum.normal,
    required this.column,
    required this.rowData,
    required this.rowIdx,
    required this.columnIdx,
    required this.rowId,
    required this.columnId,
  });

  final CellStateEnum state;

  final AppTableColumn column;

  final Map<String, dynamic> rowData;

  final int rowIdx;

  final int columnIdx;

  final String rowId;
  final String columnId;

  @override
  State<HyperlinkCell> createState() => _HyperlinkCellState();
}

class _HyperlinkCellState extends State<HyperlinkCell> {
  late AppTableColumn column;
  late AppTableColumnTypeHyperlink columnObj;
  late Map<String, dynamic> rowData;
  late String rowId;
  late String columnId;

  @override
  void initState() {
    // TODO: implement initState
    super.initState();
    column = widget.column;
    columnObj = column.type as AppTableColumnTypeHyperlink;
    rowData = widget.rowData;
    rowId = widget.rowId;
    columnId = widget.columnId;
  }

  @override
  Widget build(BuildContext context) {
    var value = rowData[column.field] ?? '';

    var state = widget.state;
    if (state == CellStateEnum.edit) {
      return AppHyperlink(
        initialValue: value,
        onChanged: (value) {
          rowData[column.field] = value.toString();
        },
      );
    } else {
      // return Text(value);

      return GestureDetector(
        onTap: () async {
          if (value.isNotEmpty) {
            final Uri url = Uri.parse(value);
            if (await canLaunchUrl(url)) {
              await launchUrl(url, mode: LaunchMode.externalApplication);
            }
          }
        },
        child: Text(
          value,
          style: TextStyle(
            color: Colors.blue,
            decoration: TextDecoration.underline,
            decorationColor: Colors.blue,
          ),
        ),
      );
    }
  }
}
