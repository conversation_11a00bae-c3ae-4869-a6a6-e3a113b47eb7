// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'role.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

Role _$RoleFromJson(Map<String, dynamic> json) => Role(
  id: json['Id'] as String?,
  name: json['Name'] as String? ?? '',
  groupId: json['GroupId'] as String? ?? '',
  defaultenum: (json['Defaultenum'] as num?)?.toInt() ?? 2,
);

Map<String, dynamic> _$RoleTo<PERSON>son(Role instance) => <String, dynamic>{
  'Id': instance.id,
  'Name': instance.name,
  'GroupId': instance.groupId,
  'Defaultenum': instance.defaultenum,
};
