import 'package:octasync_client/components/data/app_table/model/role_mgmt/role.dart';
import 'package:json_annotation/json_annotation.dart';

import 'package:json_annotation/json_annotation.dart';

part 'group_config.g.dart';

@JsonSerializable()
class GroupConfig {
  @Json<PERSON>ey(name: 'Id')
  String? id;

  @Json<PERSON>ey(name: 'Name', defaultValue: '')
  String name;

  /// 系统权限分组: 1.系统权限分组 10.项目权限分组
  @<PERSON>son<PERSON>ey(name: 'GroupTypeenum')
  final int groupTypeenum;

  /// 系统配置 1.是 2.不是
  @<PERSON><PERSON><PERSON><PERSON>(name: 'Defaultenum')
  final int defaultenum;

  @Json<PERSON>ey(name: 'RoleList', defaultValue: [])
  List<Role>? roleList;

  GroupConfig({
    this.id,
    this.name = '',
    this.groupTypeenum = 1,
    this.defaultenum = 2,
    this.roleList,
  });

  factory GroupConfig.fromJson(Map<String, dynamic> json) => _$GroupConfigFromJson(json);
  @override
  Map<String, dynamic> toJson() => _$GroupConfigToJson(this);
}
