import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:octasync_client/components/data/app_table/app_basic_text_field.dart';
// import 'package:intl/intl.dart';
import 'package:intl/intl.dart' as intl;

// 支持的格式列表
final formats = [
  {'pattern': r'^\d{4}年\d{2}月\d{2}$', 'format': 'yyyy年MM月dd日'},
  {'pattern': r'^\d{4}年\d{2}月\d{2} \d{2}:\d{2}$', 'format': 'yyyy年MM月dd日 HH:mm'},

  {'pattern': r'^\d{4}年\d{2}月$', 'format': 'yyyy年MM月'},
  {'pattern': r'^\d{4}年\d{2}月 \d{2}:\d{2}$', 'format': 'yyyy年MM月 HH:mm'},

  {'pattern': r'^\d{2}/\d{2}$', 'format': 'MM/dd'},
  {'pattern': r'^\d{2}/\d{2} \d{2}:\d{2}$', 'format': 'MM/dd HH:mm'},

  // 年月日格式
  {'pattern': r'^\d{4}-\d{2}-\d{2}$', 'format': 'yyyy-MM-dd'},
  {'pattern': r'^\d{4}/\d{2}/\d{2}$', 'format': 'yyyy/MM/dd'},

  // 完整日期时间格式 (yyyy-MM-dd HH:mm:ss)
  {'pattern': r'^\d{4}-\d{2}-\d{2} \d{2}:\d{2}:\d{2}$', 'format': 'yyyy-MM-dd HH:mm:ss'},
  {'pattern': r'^\d{4}/\d{2}/\d{2} \d{2}:\d{2}:\d{2}$', 'format': 'yyyy/MM/dd HH:mm:ss'},

  // 日期时间格式 (yyyy-MM-dd HH:mm)
  {'pattern': r'^\d{4}-\d{2}-\d{2} \d{2}:\d{2}$', 'format': 'yyyy-MM-dd HH:mm'},
  {'pattern': r'^\d{4}/\d{2}/\d{2} \d{2}:\d{2}$', 'format': 'yyyy/MM/dd HH:mm'},

  // 仅时间格式 (HH:mm:ss)
  {'pattern': r'^\d{2}:\d{2}:\d{2}$', 'format': 'HH:mm:ss'},

  // 仅时间格式 (HH:mm)
  {'pattern': r'^\d{2}:\d{2}$', 'format': 'HH:mm'},
];

class AppDate extends StatefulWidget {
  final DateTime? initialValue;

  /// 时间格式（yyyy-MM-dd || yyyy/MM/dd）——传进来的是显示是format，可能带有星期
  final String format;
  final ValueChanged<DateTime?>? onChanged;

  // final InputDecoration? decoration;

  const AppDate({
    super.key,
    this.initialValue,
    this.format = 'yyyy-MM-dd HH:mm:ss',
    this.onChanged,
    // this.decoration,
  });

  @override
  State<AppDate> createState() => _AppDateState();
}

var decoration = InputDecoration(
  border: OutlineInputBorder(borderSide: BorderSide(color: Colors.grey, width: 1)),
  contentPadding: EdgeInsets.symmetric(horizontal: 10),
  hintText: '',
);

class _AppDateState extends State<AppDate> {
  DateTime? _lastValidDate;
  late String _normalFormat;
  late String _editFormat;

  @override
  void initState() {
    super.initState();
    _initFormat();
    _lastValidDate = widget.initialValue;
  }

  @override
  void didUpdateWidget(covariant AppDate oldWidget) {
    super.didUpdateWidget(oldWidget);

    // 当 widget 参数发生变化时，重新计算格式
    if (oldWidget.format != widget.format) {
      _initFormat();
    }
  }

  void _initFormat() {
    // 转成不带有星期的格式（编辑时，去掉星期（EEEE））
    // 下面都是日期格式，传过来的是展示的日期格式，可能缺少（年月日）部分，所以编辑时，格式需要补齐

    var orgFormat = widget.format.replaceAll(' EEEE', '');
    if (orgFormat == 'yyyy年MM月') {
      orgFormat = 'yyyy年MM月dd日';
    } else if (orgFormat == 'yyyy年MM月 HH:mm') {
      orgFormat = 'yyyy年MM月dd日 HH:mm';
    } else if (orgFormat == 'MM/dd') {
      orgFormat = 'yyyy/MM/dd';
    } else if (orgFormat == 'MM/dd HH:mm') {
      orgFormat = 'yyyy/MM/dd HH:mm';
    }

    _editFormat = orgFormat;

    // 优化：验证传入的格式是否在支持的格式列表中
    bool isFormatSupported = formats.any((formatInfo) => formatInfo['format'] == _editFormat);

    // 如果传入的格式不被支持，则使用默认格式
    if (isFormatSupported) {
      _normalFormat = widget.format;
    } else {
      // 使用默认的完整日期时间格式
      _normalFormat = 'yyyy-MM-dd HH:mm:ss';
      _editFormat = 'yyyy-MM-dd HH:mm:ss';
    }
  }

  // 根据输入格式解析日期时间
  DateTime? _parseDateTime(String text, intl.DateFormat format) {
    if (text.trim().isEmpty) {
      return null;
    }

    var result = intl.DateFormat(_editFormat).parse(text.toString());

    return result;

    // try {
    // } catch (e) {
    //   return null;
    // }

    // for (var formatInfo in formats) {
    //   if (RegExp(formatInfo['pattern'] as String).hasMatch(text)) {
    //     return intl.DateFormat(formatInfo['format'] as String).parse(text);
    //   }
    // }

    // // 如果没有匹配的格式，抛出异常
    // throw FormatException('Unsupported date format');
  }

  // // 格式化日期时间用于显示/编辑
  // String _formatDateTime(DateTime date, intl.DateFormat formatter) {
  //   return formatter.format(date);
  // }

  @override
  Widget build(BuildContext context) {
    final intl.DateFormat displayFormat = intl.DateFormat(_normalFormat, 'zh');
    final intl.DateFormat editFormat = intl.DateFormat(_editFormat, 'zh');

    return AppBasicTextField(
      key: ValueKey('format_${_normalFormat}'),
      initialValue: widget.initialValue != null ? editFormat.format(widget.initialValue!) : '',
      keyboardType: TextInputType.datetime,
      // inputFormatters: [
      //   // FilteringTextInputFormatter(RegExp(testReg), allow: true),
      //   LengthLimitingTextInputFormatter(15), //限制长度
      //   // MyNumberTextInputFormatter(decimalDigits: precision), //小数位数
      // ],
      formatOnFocus: (value) {
        if (value is DateTime) {
          return editFormat.format(value);
        }
        return value.toString();
      },
      formatOnBlur: (value) {
        if (value is DateTime) {
          return displayFormat.format(value);
        }
        return value.toString();
      },
      parser: (text) {
        try {
          DateTime? parsedDate;

          // if (text.isEmpty) {
          //   // 如果输入为空，返回当前日期或上次有效日期
          //   return _lastValidDate ?? DateTime.now();
          // }

          if (text.trim().isEmpty) {
            parsedDate = null;
          } else {
            try {
              parsedDate = _parseDateTime(text, editFormat);
            } catch (e) {
              print('错误信息：${e}');
            }
          }

          // else if (RegExp(
          //   r'^\d{4}-\d{2}-\d{2} \d{2}:\d{2}:\d{2}$',
          // ).hasMatch(text)) {
          //   // 处理 yyyy-MM-dd 格式
          //   parsedDate = intl.DateFormat('yyyy-MM-dd HH:mm:ss').parse(text);
          // } else if (RegExp(
          //   r'^\d{4}/\d{2}/\d{2} \d{2}:\d{2}:\d{2}$',
          // ).hasMatch(text)) {
          //   // 处理 yyyy-MM-dd 格式
          //   parsedDate = intl.DateFormat('yyyy/MM/dd HH:mm:ss').parse(text);
          // } else {
          //   // 格式不正确，抛出异常使用上次有效日期
          //   throw FormatException('错误的日期格式');
          // }

          // 保存有效日期
          _lastValidDate = parsedDate;
          return parsedDate;
        } catch (e) {
          // 解析失败，返回上次有效日期或当前日期
          // print('日期格式转换失败: $e, 使用上次有效日期或当前日期');
          return _lastValidDate; // ?? DateTime.now();
        }
      },
      onParsedChanged: (value) {
        // 由于 AppBasicTextField 的 onParsedChanged 传递的是 String，
        // 我们需要重新解析它

        if (widget.onChanged != null) {
          try {
            DateTime? parsedDate;
            if (value.isEmpty) {
              parsedDate = null;
              // parsedDate = _lastValidDate ?? DateTime.now();
            } else {
              parsedDate = _parseDateTime(value, editFormat);
            }

            // else if (RegExp(
            //   r'^\d{4}-\d{2}-\d{2} \d{2}:\d{2}:\d{2}$',
            // ).hasMatch(value)) {
            //   parsedDate = intl.DateFormat('yyyy-MM-dd HH:mm:ss').parse(value);
            // } else if (RegExp(
            //   r'^\d{4}/\d{2}/\d{2} \d{2}:\d{2}:\d{2}$',
            // ).hasMatch(value)) {
            //   parsedDate = intl.DateFormat('yyyy/MM/dd HH:mm:ss').parse(value);
            // } else {
            //   throw FormatException('Invalid format');
            // }

            // 更新最后有效日期
            _lastValidDate = parsedDate;
            widget.onChanged!(parsedDate);
          } catch (e) {
            // 如果解析失败，使用上次的有效日期
            if (value.trim().isEmpty) {
              widget.onChanged!(null);
            } else if (_lastValidDate != null) {
              widget.onChanged!(_lastValidDate!);
            }
          }
        }
      },
      decoration: decoration,
    );
  }
}
