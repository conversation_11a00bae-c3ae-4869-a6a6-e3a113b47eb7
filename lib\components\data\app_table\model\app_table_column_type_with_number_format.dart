import 'package:intl/intl.dart' as intl;
import 'package:octasync_client/components/data/app_table/helper/app_table_general_helper.dart';

mixin AppTableColumnTypeWithNumberFormat {
  intl.NumberFormat get numberFormat;

  bool get negative;

  int get decimalPoint;

  bool get allowFirstDot;

  String? get locale;

  // 添加抽象属性，强制实现类提供这些属性
  int get precision;
  bool get isShowPercentiles;
  bool get isRetainDecimal;

  bool isValid(dynamic value) {
    if (!isNumeric(value)) {
      return false;
    }

    if (negative == false && num.parse(value.toString()) < 0) {
      return false;
    }

    return true;
  }

  int compare(dynamic a, dynamic b) {
    return AppTableGeneralHelper.compareWithNull(
      a,
      b,
      () => toNumber(a.toString()).compareTo(toNumber(b.toString())),
    );
  }

  dynamic markCompareValue(dynamic value) {
    return value.runtimeType != num ? num.tryParse(value.toString()) ?? 0 : value;
  }

  String applyFormat(dynamic value) {
    num number =
        num.tryParse(value.toString().replaceAll(numberFormat.symbols.DECIMAL_SEP, '.')) ?? 0;

    if (negative == false && number < 0) {
      number = 0;
    }

    return numberFormat.format(number);
  }

  /// Convert [String] converted to [applyFormat] to [number].
  dynamic toNumber(String formatted) {
    String match = '0-9\\-${numberFormat.symbols.DECIMAL_SEP}';

    if (negative) {
      match += numberFormat.symbols.MINUS_SIGN;
    }

    formatted = formatted
        .replaceAll(RegExp('[^$match]'), '')
        .replaceFirst(numberFormat.symbols.DECIMAL_SEP, '.');

    final num formattedNumber = num.tryParse(formatted) ?? 0;

    return formattedNumber.isFinite ? formattedNumber : 0;
  }

  bool isNumeric(dynamic v) {
    if (v == null) {
      return false;
    }
    return num.tryParse(v.toString()) != null;
  }
}
