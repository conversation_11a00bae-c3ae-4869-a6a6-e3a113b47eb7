import 'dart:convert';

import 'package:dio/dio.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:octasync_client/components/data/app_table/app_basic_text_field.dart';
import 'package:octasync_client/components/data/app_table/app_table.dart';
import 'package:octasync_client/components/data/app_table/app_table_column_type.dart';
import 'package:octasync_client/components/data/app_table/enums/table_checked_enum.dart';
import 'package:octasync_client/components/data/app_table/helper/app_table_general_helper.dart';
import 'package:octasync_client/components/data/app_table/model/app_table_column.dart';
import 'package:octasync_client/components/data/app_table/model/role_mgmt/employee.dart';
import 'package:octasync_client/components/data/app_table/model/role_mgmt/group_config.dart';
import 'package:octasync_client/components/data/app_table/model/role_mgmt/role.dart';
import 'package:octasync_client/components/data/app_table/state/app_table_state_manage.dart';
import 'package:octasync_client/views/admin/role_auth_management/btn.dart';
import 'package:octasync_client/views/admin/role_auth_management/create_role_dialog.dart';
import 'package:octasync_client/views/admin/role_auth_management/create_role_group_dialog.dart';
import 'package:octasync_client/views/admin/role_auth_management/role_mgmt_state.dart';

import 'package:provider/provider.dart';

class RoleMgmtIndex extends StatefulWidget {
  const RoleMgmtIndex({super.key});

  @override
  State<RoleMgmtIndex> createState() => _RoleMgmtIndexState();
}

class _RoleMgmtIndexState extends State<RoleMgmtIndex> {
  Dio dio = Dio();
  final String _token =
      "tCLQz+0RWr9GGU1b43xolDcdnBBZxLZFxfg9QszX6Wem9LbM34dsIe1qcLLuM1K4tjZAH86g8nvUdKkEj0Pt9d+3O3FTMatVTMKaPecZ7DXBpp/9PsfGGNHg5FqujNxLvZz/Dhrx1IBFVpo34uqBSCJnqG1091Vmc/XtLwUhsbEZvhovWl+sTzZIZy1cdjnLXHPSc4Q/QEm0DyTo3lxu1S/9C5BoZOV2vBWiwzpJtEs=";

  var lineColor = Colors.red;
  double leftPanelWidth = 280;
  double titleHeight = 48;

  late AppTableStateManage _stateManage;

  String _keywords = '';

  var colsTemp = [
    AppTableColumn(
      text: '姓名',
      field: 'Name',
      type: AppTableColumnType.text(),
      width: 100 + 200,
      resizable: true, // 允许调整列宽
      frozen: AppTableColumnFrozen.start,
    ),
    AppTableColumn(
      text: '工号',
      field: 'Number',
      type: AppTableColumnType.text(),
      width: 100 + 50,
      resizable: true, // 允许调整列宽
    ),
    AppTableColumn(
      text: '部门',
      field: 'manager',
      type: AppTableColumnType.text(),
      // alignment: Alignment.center,
      width: 100 + 50,
      resizable: true, // 允许调整列宽
      // headerBuilder: (context) {
      //   return Row(
      //     mainAxisAlignment: MainAxisAlignment.center,
      //     children: [
      //       Text('自定义列名称'),
      //       SizedBox(width: 4),
      //       Icon(Icons.star_border, size: 16, color: Colors.red),
      //     ],
      //   );
      // },
      cellBuilder: (context, value, column, row) {
        // 测试
        // String activeValueTemp = '行3列3';
        // return Container(
        //   padding: EdgeInsets.symmetric(horizontal: 8, vertical: 4),
        //   decoration: BoxDecoration(
        //     color: value == activeValueTemp ? Colors.green : Colors.red,
        //     borderRadius: BorderRadius.circular(4),
        //   ),
        //   child: Text(
        //     value == activeValueTemp ? '活跃' : '禁用',
        //     style: TextStyle(color: Colors.white),
        //   ),
        // );
        return Text('暂无数据，待对接');
      },
    ),

    AppTableColumn(
      text: '操作',
      field: 'Opt',
      type: AppTableColumnType.text(),
      // alignment: Alignment.center,
      width: 150,
      resizable: true, // 允许调整列宽
      showMore: false,
      // headerBuilder: (context) {
      //   return Row(
      //     mainAxisAlignment: MainAxisAlignment.center,
      //     children: [
      //       Text('自定义列名称'),
      //       SizedBox(width: 4),
      //       Icon(Icons.star_border, size: 16, color: Colors.red),
      //     ],
      //   );
      // },
      cellBuilder: (context, value, column, row) {
        return TextButton(
          onPressed: () {
            showDialog(
              context: context,
              builder:
                  (ctx) => AlertDialog(
                    title: Text('确认删除'),
                    content: Text('是否确认删除当前行？'),
                    actions: [
                      TextButton(onPressed: () => Navigator.pop(ctx), child: Text('取消')),
                      TextButton(
                        onPressed: () {
                          // 直接使用 _stateManage
                          Employee emp = Employee.fromJson(row);
                          final tableState = context.read<AppTableStateManage>();
                          tableState.removeRow(emp.employeeId!);

                          Navigator.pop(ctx);
                        },
                        child: Text('删除'),
                      ),
                    ],
                  ),
            );
          },
          child: Text('删除'),
        );
      },
    ),

    // AppTableColumn(
    //   text: '人数',
    //   field: 'count',
    //   type: AppTableColumnType.number(
    //     defaultValue: 0,
    //     precision: 2,
    //     negative: false,
    //     format: '#,###', // 每三位数字使用逗号
    //   ),
    //   width: 100 + 100,
    //   resizable: true, // 允许调整列宽
    // ),
  ];

  void _showDeleteDialog(BuildContext context, Map<String, dynamic> row) {
    showDialog(
      context: context,
      builder:
          (context) => AlertDialog(
            title: Text('确认删除'),
            content: Text('是否确认删除当前行？'),
            actions: [
              TextButton(onPressed: () => Navigator.pop(context), child: Text('取消')),
              TextButton(
                onPressed: () {
                  // 直接使用 _stateManage
                  Employee emp = Employee.fromJson(row);
                  print('删除员工: ${emp.name}----${emp.employeeId}');

                  // 使用 _stateManage 操作数据
                  // 注意：需要确保 _stateManage 已经初始化
                  if (_stateManage != null) {
                    // 执行删除操作
                    // _stateManage.removeRowById(emp.employeeId);
                  }

                  Navigator.pop(context);
                },
                child: Text('删除'),
              ),
            ],
          ),
    );
  }

  @override
  Widget build(BuildContext context) {
    return ChangeNotifierProvider(
      create: (_) => RoleMgmtState(),
      child: Builder(
        builder:
            (context) => LayoutBuilder(
              builder: (ctx, constraints) {
                return Scaffold(
                  appBar: AppBar(title: Text('静态table配置demo')),
                  body: Stack(
                    children: [
                      Column(
                        children: [
                          _TitleBlock(title: '角色管理'),
                          Expanded(
                            child: Row(
                              children: [
                                Container(
                                  width: leftPanelWidth,
                                  decoration: BoxDecoration(
                                    border: Border(right: BorderSide(color: lineColor, width: 1)),
                                  ),
                                  child: Padding(
                                    padding: EdgeInsets.symmetric(horizontal: 16),
                                    child: Column(
                                      crossAxisAlignment: CrossAxisAlignment.start,
                                      mainAxisSize: MainAxisSize.max,

                                      children: [
                                        Container(
                                          padding: EdgeInsets.symmetric(vertical: 13),
                                          child: AppBasicTextField(
                                            initialValue: _keywords,
                                            onChanged: (value) {
                                              _keywords = value;
                                              setState(() {});
                                            },
                                            decoration: InputDecoration(
                                              border: OutlineInputBorder(
                                                // borderSide: BorderSide(
                                                //   color: Colors.grey,
                                                //   width: 1,
                                                // ),
                                              ),
                                              contentPadding: EdgeInsets.symmetric(horizontal: 10),
                                              hintText: '请输入关键字',
                                            ),
                                          ),
                                        ),

                                        Row(
                                          children: [
                                            btn(
                                              width: 78,
                                              title: '创建分组',
                                              onTap: () {
                                                ctx.read<RoleMgmtState>().handleGroupCreate();
                                              },
                                            ),
                                            SizedBox(width: 10),
                                            btn(
                                              width: 78,
                                              title: '创建角色',
                                              onTap: () {
                                                ctx.read<RoleMgmtState>().handleRoleCreate();
                                              },
                                            ),
                                            Spacer(),
                                            IconButton(
                                              onPressed: () {
                                                getUserList();
                                              },
                                              icon: Icon(Icons.expand),
                                            ),
                                          ],
                                        ),

                                        //分组列表
                                        _buildGroupList(ctx),
                                      ],
                                    ),
                                  ),
                                ),
                                Expanded(
                                  child: Column(
                                    crossAxisAlignment: CrossAxisAlignment.start,
                                    mainAxisSize: MainAxisSize.max,
                                    children: [
                                      _TitleBlock(
                                        key: ValueKey('title_2'),
                                        title: '权限配置',
                                        lineColor: lineColor,
                                        titleHeight: titleHeight,
                                      ),

                                      Row(children: [Text('按钮操作区域')]),
                                      Expanded(
                                        child: Padding(
                                          padding: EdgeInsets.all(10),
                                          child: AppTable(
                                            // key: ValueKey(currentTabId),
                                            // loading: isLoading,
                                            // columns: columns,
                                            // rows: rows,
                                            // columns: [],
                                            // rows: [],
                                            checkType: TableCheckedEnum.multiple,
                                            indexColumnWidth: 100,
                                            defaultExpandAll: true,
                                            maxLevel: 6,

                                            uniqueId: 'EmployeeId',

                                            /// 树形结构配置（扁平结构不需要传递树形结构配置参数）
                                            // treeConfig: TreeTableConfig(
                                            //   idField: 'nodeId',
                                            //   parentIdField: 'pid',
                                            //   emptyParentValue: null,
                                            // ),
                                            showAddRowButton: false,
                                            showAddColumnButton: false,
                                            // listviewPddingBottom: 0,

                                            /// 索引列自定义
                                            // indexCellBuilder: (context, index) {
                                            //   return Text('第${index}条');
                                            // },
                                            onCellTap: (context, rowIndex, columnIndex, value) {
                                              String msg =
                                                  '点击了第${rowIndex}行，第${columnIndex}列，值为：$value';
                                              print(msg);
                                            },
                                            onLoaded: (stateManage) {
                                              _stateManage = stateManage;
                                              // getList(true);

                                              _stateManage.setColumns(colsTemp);
                                            },
                                            onLoadMore: () {
                                              // loadMore();
                                            },

                                            // onAddColumn: (
                                            //   context,
                                            //   AppTableColumn column,
                                            //   AppTableStateManage stateManager,
                                            //   double tableViewportWidth,
                                            // ) {
                                            //   print('添加列：${column}');

                                            //   if (columns.length < 10) {
                                            //     setState(() {
                                            //       //添加新列
                                            //       columns.add(column);

                                            //       //新增列的唯一标识符
                                            //       var newField = column.field;

                                            //       print(newField);

                                            //       // //所有的行补充新增列属性
                                            //       // for (var row in rows) {
                                            //       //   row.data[newField] = '';
                                            //       // }
                                            //     });

                                            //     // WidgetsBinding.instance.addPostFrameCallback((_) {
                                            //     //   //直接使用传入的状态管理实例执行滚动调整逻辑
                                            //     //   stateManager.adjustScrollPositionAfterAddColumn(
                                            //     //     tableViewportWidth,
                                            //     //     column.width,
                                            //     //   );
                                            //     // });
                                            //   } else {
                                            //     print('表格列不能超过10列-------------------');
                                            //   }

                                            //   // print('添加了新列：${column.field} ${column.text} ${column.width}');

                                            //   // AppTableGeneralHelper.newColumn(1000);
                                            // },
                                          ),
                                        ),
                                      ),
                                    ],
                                  ),
                                ),
                              ],
                            ),
                          ),
                        ],
                      ),

                      _buildCreateRoleGroupDialog(ctx, constraints),
                      _buildCreateRoleDialog(ctx, constraints),
                    ],
                  ),
                );
              },
            ),
      ),
    );
  }

  //创建角色分组弹框
  Widget _buildCreateRoleGroupDialog(BuildContext context, BoxConstraints parentCconstraints) {
    var proData = context.select<RoleMgmtState, ({bool showCreateRoleGroupDialog})>(
      (provider) => (showCreateRoleGroupDialog: provider.showCreateRoleGroupDialog),
    );

    var left = (parentCconstraints.maxWidth - 500) / 2;
    var top = (parentCconstraints.maxHeight - 200) / 2;

    return proData.showCreateRoleGroupDialog
        ? Positioned(left: left, top: top, child: CreateRoleGroupDialog())
        : const SizedBox.shrink();
  }

  //创建角色弹框
  Widget _buildCreateRoleDialog(BuildContext context, BoxConstraints parentCconstraints) {
    var proData = context.select<RoleMgmtState, ({bool showCreateRoleDialog})>(
      (provider) => (showCreateRoleDialog: provider.showCreateRoleDialog),
    );

    var left = (parentCconstraints.maxWidth - 500) / 2;
    var top = (parentCconstraints.maxHeight - 200) / 2;

    return proData.showCreateRoleDialog
        ? Positioned(left: left, top: top, child: CreateRoleDialog())
        : const SizedBox.shrink();
  }

  BaseOptions _getOption() {
    const String APPLICATION_JSON = "application/json; charset=utf-8";

    return BaseOptions(
      connectTimeout: Duration(seconds: 10),
      receiveTimeout: Duration(seconds: 10),
      headers: {'Content-Type': APPLICATION_JSON, 'Accept': APPLICATION_JSON, 'token': _token},
      validateStatus: (status) {
        // 允许所有状态码，手动处理错误
        return status != null && status < 500;
      },
    );
  }

  Future<void> getList() async {
    try {
      dio.options = _getOption();

      var response = await dio.post('http://192.168.99.100:7001/api/Business/Role/GetGroupList');

      if (response.statusCode == 200) {
        var listJson = response.data['TMessageData'];

        // print('获取到分组数据：');
        // print(jsonEncode(listJson));

        // List<GroupConfig> list =
        //     listJson.map((e) => GroupConfig.fromJson(e)).toList()
        //         as List<GroupConfig>;

        List<GroupConfig> list =
            (listJson as List).map((e) => GroupConfig.fromJson(e as Map<String, dynamic>)).toList();

        // print('转换成功');

        // for (var i = 0; i < list.length; i++) {
        //   print(jsonEncode(list[1].roleList));

        //   // print(Role.fromJson(list[i].roleList[0]));
        // }

        // AppTableGeneralHelper.showMessage('保存分组————————成功');
      } else {
        AppTableGeneralHelper.showMessage('请求表格列表————————失败 ${response.statusCode}');
      }
    } catch (e) {
      _stateManage.setLoading(false);
      print('getList 异常: $e');
    }
  }

  Future<void> getUserList() async {
    try {
      dio.options = _getOption();
      // _stateManage.clearRows();
      _stateManage.setLoading(true);
      var response = await dio.post(
        'http://192.168.99.100:7001/api/Business/Role/GetRoleEmployeeList',
        data: {
          "PageSize": 10000,
          "PageIndex": 1,
          "RoleId": "0198abab-c804-74b7-be3d-7086fd86f35b", // 0198abab-f9b5-70a4-8c18-e6dd4f1a0175
        },
      );

      if (response.statusCode == 200) {
        _stateManage.setLoading(false);
        var listJson = response.data['TMessageData']['Items'];

        List<Employee> list =
            (listJson as List).map((e) => Employee.fromJson(e as Map<String, dynamic>)).toList();

        // print('获取到用户列表数据：');
        // print(jsonEncode(list));

        List<Map<String, dynamic>> jsonRowsStr =
            listJson.map((r) => r as Map<String, dynamic>).toList();
        // (listJson as List).map((r) => r).cast<Map<String, dynamic>>();
        var rows = _stateManage.convertToTreeData(jsonRowsStr);

        _stateManage.setRows(rows);

        // WidgetsBinding.instance.addPostFrameCallback((_) {});

        // var list = listJson.map((e) => GroupConfig.fromJson(e)).toList();

        // for (var i = 0; i < list.length; i++) {
        //   print(jsonEncode(list[i].roleList));
        // }

        AppTableGeneralHelper.showMessage('保存分组————————成功');
      } else {
        _stateManage.setLoading(false);
        AppTableGeneralHelper.showMessage('请求表格列表————————失败 ${response.statusCode}');
      }
    } catch (e) {
      _stateManage.setLoading(false);
      print('getList 异常: $e');
    }
  }

  Widget _buildGroupList(BuildContext context) {
    //刷新列表
    var reloadGroupListFlag = context.select<RoleMgmtState, int>(
      (state) => state.reloadGroupListFlag,
    );
    //列表集合

    getList();

    return Column(children: [Text(reloadGroupListFlag.toString())]);
  }
}

// 左右区块头标题组件
class _TitleBlock extends StatelessWidget {
  final String title;
  final double titleHeight;
  final Color lineColor;

  const _TitleBlock({
    super.key,
    required this.title,
    this.titleHeight = 48,
    this.lineColor = Colors.red,
  });

  @override
  Widget build(BuildContext context) {
    return Container(
      height: titleHeight,
      width: double.infinity,
      decoration: BoxDecoration(border: Border(bottom: BorderSide(color: lineColor, width: 1))),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.start, // 左对齐
        crossAxisAlignment: CrossAxisAlignment.center, // 垂直居中
        children: [
          Padding(
            padding: EdgeInsets.symmetric(horizontal: 10),
            child: Text(
              title,
              style: TextStyle(fontSize: 16, fontWeight: FontWeight.w500, color: Colors.red),
            ),
          ),
        ],
      ),
    );
  }
}
