import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';

class btn extends StatelessWidget {
  final String title;
  final double width;
  final VoidCallback? onTap;

  const btn({super.key, required this.title, this.width = 100, this.onTap});

  @override
  Widget build(BuildContext context) {
    return Container(
      width: width,
      child: ElevatedButton(
        child: Text(title),
        style: ElevatedButton.styleFrom(
          backgroundColor: Colors.blue,
          foregroundColor: Colors.white,
          padding: EdgeInsets.symmetric(horizontal: 10),
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.all(Radius.circular(5)), // 取消圆角
          ),
        ),
        onPressed: onTap,
      ),
    );
  }
}
